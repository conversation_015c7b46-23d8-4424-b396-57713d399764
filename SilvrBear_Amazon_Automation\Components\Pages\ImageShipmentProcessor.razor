@page "/image-shipment"
@rendermode @(InteractiveServer)
@using SilvrBear_Amazon_Automation.Models
@using SilvrBear_Amazon_Automation.Services
@using SilvrBear_Amazon_Automation.Constants
@using SilvrBear_Amazon_Automation.Helpers
@using SilvrBear_Amazon_Automation.Components.Shared
@using Microsoft.AspNetCore.Components.Forms
@using Microsoft.AspNetCore.Components.Web
@inject IImageToShipmentService ImageToShipmentService
@inject IProductMappingService ProductMappingService
@inject ILogger<ImageShipmentProcessor> Logger
@inject IJSRuntime JSRuntime

<PageTitle>Image to Shipment Processor</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="mb-4">
                <h2>
                    <i class="bi bi-camera"></i> Image to Shipment Processor
                </h2>
                <p class="text-muted mb-0">Upload a shipment image, validate the data, and create Amazon FBA inbound shipments</p>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(statusMessage))
    {
        <div class="alert @(isSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
            @statusMessage
            <button type="button" class="btn-close" @onclick="ClearStatus"></button>
        </div>
    }

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-upload"></i> Upload Shipment Image
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Shipment Name</label>
                                <input type="text" class="form-control" @bind="shipmentName" 
                                       placeholder="Enter shipment name (optional)" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Fulfillment Center</label>
                                <select class="form-select" @bind="selectedFulfillmentCenter">
                                    @foreach (var fc in ShipmentConstants.FulfillmentCenters.IndiaFulfillmentCenters)
                                    {
                                        <option value="@fc.Key" selected="@(fc.Key == ShipmentConstants.FulfillmentCenters.DefaultFulfillmentCenterId)">
                                            @fc.Value
                                        </option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Image File</label>
                        <InputFile OnChange="HandleImageSelection" class="form-control" accept="image/*" />
                        <div class="form-text">
                            Supported formats: JPG, PNG, BMP, GIF. Maximum size: @(ShipmentConstants.FileProcessing.MaxImageSizeMB)MB
                        </div>
                    </div>

                    @if (selectedImageFile != null)
                    {
                        <div class="mb-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Selected Image</h6>
                                    <p class="card-text">
                                        <strong>File:</strong> @selectedImageFile.Name<br />
                                        <strong>Size:</strong> @FormatFileSize(selectedImageFile.Size)<br />
                                        <strong>Type:</strong> @selectedImageFile.ContentType
                                    </p>                    <div class="small text-muted mt-2">
                        <strong>Debug Info:</strong><br />
                        selectedImageFile != null: @(selectedImageFile != null)<br />
                        isProcessing: @isProcessing<br />
                        isValidated: @isValidated
                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-success" @onclick="ValidateImage"
                                disabled="@(selectedImageFile == null || isProcessing)">
                            @if (isProcessing && currentOperation == "validate")
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            <i class="bi bi-check-circle"></i> Validate Image
                        </button>

                        <button type="button" class="btn btn-warning" @onclick="ProcessImageOnly"
                                disabled="@(selectedImageFile == null || isProcessing || !isValidated)">
                            @if (isProcessing && currentOperation == "process")
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            <i class="bi bi-gear"></i> Process Data Only
                        </button>

                        <button type="button" class="btn btn-primary" @onclick="ProcessAndCreateShipment"
                                disabled="@(selectedImageFile == null || isProcessing || !isDataProcessed)">
                            @if (isProcessing && currentOperation == "create")
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            <i class="bi bi-box-arrow-up"></i> Create Shipment
                        </button>
                    </div>

                    @if (isProcessing)
                    {
                        <div class="mt-3">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 100%">
                                    Processing @currentOperation...
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>

            @if (processingResult != null && processingResult.JsonExtracted)
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pencil-square"></i> Edit Extracted Data
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>Review and edit the extracted data below.</strong>
                            Make any necessary corrections before processing the data.
                        </div>

                        @if (processingResult.ExtractedBoxData != null && processingResult.ExtractedBoxData.Any())
                        {
                            <JsonTableEditor BoxData="processingResult.ExtractedBoxData"
                                           OnDataSaved="OnJsonDataSaved"
                                           OnDataChanged="OnJsonDataChanged" />
                        }
                        else
                        {
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                <strong>Debug Info:</strong>
                                ExtractedBoxData is @(processingResult.ExtractedBoxData == null ? "null" : $"empty (count: {processingResult.ExtractedBoxData.Count})")
                                <br />
                                JsonExtracted: @processingResult.JsonExtracted
                                <br />
                                IsSuccess: @processingResult.IsSuccess
                                @if (processingResult.Errors.Any())
                                {
                                    <br />
                                    <strong>Errors:</strong> @string.Join(", ", processingResult.Errors)
                                }
                            </div>
                        }
                    </div>
                </div>
            }

            @if (processingResult != null && processingResult.ImageProcessingResult != null)
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-check"></i> Extracted Box Data
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (processingResult.ImageProcessingResult.ProcessedBoxes.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Box #</th>
                                            <th>Original Product</th>
                                            <th>Mapped Product</th>
                                            <th>SKU</th>
                                            <th>Qty</th>
                                            <th>Unit Weight (kg)</th>
                                            <th>Dimensions</th>
                                            <th>Total Weight (kg)</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var box in processingResult.ImageProcessingResult.ProcessedBoxes)
                                        {
                                            <tr class="@(box.IsValid ? "" : "table-warning")">
                                                <td>@box.BoxNumber</td>
                                                <td>@box.OriginalProductName</td>
                                                <td>@box.MappedProductName</td>
                                                <td><code>@box.GeneratedSKU</code></td>
                                                <td>@box.Quantity</td>
                                                <td><strong>@box.ItemWeightKg.ToString("F3")</strong></td>
                                                <td>@box.DimensionsString</td>
                                                <td>@box.TotalItemWeightKg.ToString("F2")</td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center text-muted py-4">
                                <i class="bi bi-exclamation-circle display-4"></i>
                                <p class="mt-2">No boxes extracted from the image</p>
                            </div>
                        }
                    </div>
                </div>
            }

            @if (processingResult != null && processingResult.ConsolidatedItems.Any())
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-ul"></i> Item Details List (for Amazon API)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>SKU</th>
                                        <th>Product Name</th>
                                        <th>Total Quantity</th>
                                        <th>Unit Weight (kg)</th>
                                        <th>Total Weight (kg)</th>
                                        <th>Barcode Type</th>
                                        <th>Source Boxes</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var item in processingResult.ConsolidatedItems)
                                    {
                                        <tr>
                                            <td><code>@item.Sku</code></td>
                                            <td>@item.ProductName</td>
                                            <td><strong>@item.TotalQuantity</strong></td>
                                            <td>@item.UnitWeightKg.ToString("F3")</td>
                                            <td><strong>@item.TotalWeightKg.ToString("F2")</strong></td>
                                            <td>
                                                @if (!string.IsNullOrEmpty(item.BarcodeType))
                                                {
                                                    <span class="badge bg-secondary">@item.BarcodeType</span>
                                                }
                                            </td>
                                            <td>@string.Join(", ", item.SourceBoxNumbers)</td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        <div class="mt-2">
                            <small class="text-muted">
                                <strong>Summary:</strong> @processingResult.UniqueSkuCount unique SKUs,
                                Total Weight: @processingResult.TotalShipmentWeightKg.ToString("F2") kg
                            </small>
                        </div>
                    </div>
                </div>
            }

            @if (processingResult != null && processingResult.ConsolidatedBoxes.Any())
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-box"></i> Box Details List (for Amazon API)
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered">
                                <thead class="table-light">
                                    <tr>
                                        <th>Box #</th>
                                        <th>Weight (kg)</th>
                                        <th>Weight (lbs)</th>
                                        <th>Dimensions</th>
                                        <th>Contents</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var box in processingResult.ConsolidatedBoxes)
                                    {
                                        <tr>
                                            <td>@box.BoxNumber</td>
                                            <td><strong>@box.TotalWeightKg.ToString("F2")</strong></td>
                                            <td>@box.TotalWeightPounds.ToString("F2")</td>
                                            <td>@box.DimensionsString</td>
                                            <td>
                                                @if (box.Items.Any())
                                                {
                                                    @foreach (var item in box.Items.Select((item, index) => new { item, index }))
                                                    {
                                                        <span class="small">
                                                            <code>@item.item.Sku</code>: @item.item.Quantity pcs 
                                                            <span class="text-muted">(@item.item.UnitWeightKg.ToString("F3") kg each)</span>
                                                            <span class="text-muted">Total: @item.item.TotalWeightKg.ToString("F2") kg</span>@(item.index < box.Items.Count - 1 ? ", " : "")
                                                        </span>
                                                    }
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            }
        </div>

        <div class="col-lg-4">
            <!-- Product Mapping Status removed as part of UI cleanup -->

            @if (processingResult != null)
            {
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-info-circle"></i> Processing Result
                        </h6>
                    </div>
                    <div class="card-body">
                        @if (processingResult.IsSuccess)
                        {
                            <div class="text-success mb-2">
                                <i class="bi bi-check-circle"></i> Processing successful
                            </div>
                            
                            @if (!string.IsNullOrEmpty(processingResult.ShipmentId))
                            {
                                <div class="alert alert-success">
                                    <strong>Shipment Created!</strong><br />
                                    <strong>ID:</strong> @processingResult.ShipmentId<br />
                                    <strong>Time:</strong> @processingResult.ProcessedAt.ToString("HH:mm:ss")
                                </div>
                            }

                            <ul class="list-unstyled small">
                                <li><strong>Total Boxes:</strong> @processingResult.TotalBoxesProcessed</li>
                                <li><strong>Valid Boxes:</strong> @processingResult.ValidBoxes</li>
                                <li><strong>Invalid Boxes:</strong> @processingResult.InvalidBoxes</li>
                                <li><strong>Processing Time:</strong> @processingResult.TotalProcessingTime.TotalSeconds.ToString("F1")s</li>
                            </ul>
                        }
                        else
                        {
                            <div class="text-danger mb-2">
                                <i class="bi bi-exclamation-circle"></i> Processing failed
                            </div>
                        }

                        @if (processingResult.Errors.Any())
                        {
                            <div class="mt-2">
                                <strong class="text-danger">Errors:</strong>
                                <ul class="list-unstyled small">
                                    @foreach (var error in processingResult.Errors)
                                    {
                                        <li class="text-danger">• @error</li>
                                    }
                                </ul>
                            </div>
                        }

                        @if (processingResult.Warnings.Any())
                        {
                            <div class="mt-2">
                                <strong class="text-warning">Warnings:</strong>
                                <ul class="list-unstyled small">
                                    @foreach (var warning in processingResult.Warnings)
                                    {
                                        <li class="text-warning">• @warning</li>
                                    }
                                </ul>
                            </div>
                        }
                    </div>
                </div>
            }

            <!-- Processing Statistics removed as part of UI cleanup -->
        </div>
    </div>
</div>

@code {
    private IBrowserFile? selectedImageFile;
    private string shipmentName = "";
    private string selectedFulfillmentCenter = ShipmentConstants.FulfillmentCenters.DefaultFulfillmentCenterId;
    private string statusMessage = "";
    private bool isSuccess = false;
    private bool isProcessing = false;
    private bool isValidated = false;
    private bool isDataProcessed = false;
    private string currentOperation = "";

    private ImageToShipmentResult? processingResult;
    // Note: Statistics variables removed as part of cleanup

    protected override async Task OnInitializedAsync()
    {
        try
        {
            // Note: Statistics loading removed as part of cleanup
            Logger.LogInformation("Image to Shipment Processor initialized");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error initializing component");
            SetStatusMessage("Error loading initial data", false);
        }
    }

    private void HandleImageSelection(InputFileChangeEventArgs e)
    {
        selectedImageFile = e.File;
        isValidated = false;
        isDataProcessed = false;
        processingResult = null;

        if (selectedImageFile != null)
        {
            SetStatusMessage($"Image selected: {selectedImageFile.Name}", true);
            Logger.LogInformation("Image file selected: {FileName}, Size: {Size}",
                selectedImageFile.Name, selectedImageFile.Size);
        }
        else
        {
            SetStatusMessage("No image selected", false);
            Logger.LogInformation("No image file selected");
        }

        // Force UI update
        StateHasChanged();
    }

    private async Task ValidateImage()
    {
        if (selectedImageFile == null)
        {
            SetStatusMessage("Please select an image file first.", false);
            return;
        }

        try
        {
            isProcessing = true;
            currentOperation = "validate";
            SetStatusMessage("Extracting data from image...", true);

            Logger.LogInformation("Starting JSON extraction for file: {FileName}", selectedImageFile.Name);

            var formFile = new BrowserFileAdapter(selectedImageFile);
            processingResult = await ImageToShipmentService.ExtractJsonFromImageAsync(
                formFile,
                shipmentName,
                selectedFulfillmentCenter);

            if (processingResult.IsSuccess && processingResult.JsonExtracted)
            {
                isValidated = true;
                SetStatusMessage($"JSON extraction successful! Found {processingResult.ValidBoxes} boxes. You can now edit the data before processing.", true);
                Logger.LogInformation("JSON extraction successful for file: {FileName}. Found {BoxCount} boxes",
                    selectedImageFile.Name, processingResult.ExtractedBoxData.Count);
            }
            else
            {
                var errorMessage = string.Join("; ", processingResult.Errors);
                SetStatusMessage($"JSON extraction failed: {errorMessage}", false);
                Logger.LogWarning("JSON extraction failed for file: {FileName}. Errors: {Errors}",
                    selectedImageFile.Name, errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error extracting JSON from image: {FileName}", selectedImageFile?.Name ?? "Unknown");
            SetStatusMessage($"JSON extraction error: {ex.Message}", false);
        }
        finally
        {
            isProcessing = false;
            currentOperation = "";
            StateHasChanged();
        }
    }

    private async Task ProcessImageOnly()
    {
        if (processingResult == null || !processingResult.JsonExtracted || !processingResult.ExtractedBoxData.Any())
        {
            SetStatusMessage("Please extract JSON data first.", false);
            return;
        }

        try
        {
            isProcessing = true;
            currentOperation = "process";
            SetStatusMessage("Processing extracted data...", true);

            Logger.LogInformation("Starting data processing with {BoxCount} extracted boxes", processingResult.ExtractedBoxData.Count);

            // Use the edited JSON data for processing
            var updatedResult = await ImageToShipmentService.ProcessExtractedDataAsync(
                processingResult.ExtractedBoxData,
                shipmentName,
                selectedFulfillmentCenter);

            // Merge results while preserving the extracted JSON data
            processingResult.ImageProcessingResult = updatedResult.ImageProcessingResult;
            processingResult.ConsolidatedItems = updatedResult.ConsolidatedItems;
            processingResult.ConsolidatedBoxes = updatedResult.ConsolidatedBoxes;
            processingResult.UniqueSkuCount = updatedResult.UniqueSkuCount;
            processingResult.TotalShipmentWeightKg = updatedResult.TotalShipmentWeightKg;
            processingResult.ValidBoxes = updatedResult.ValidBoxes;
            processingResult.InvalidBoxes = updatedResult.InvalidBoxes;
            processingResult.DataProcessed = updatedResult.DataProcessed;
            processingResult.Errors.AddRange(updatedResult.Errors);
            processingResult.Warnings.AddRange(updatedResult.Warnings);

            if (updatedResult.IsSuccess)
            {
                isDataProcessed = true;
                processingResult.IsSuccess = true;
                SetStatusMessage($"Data processing successful! Found {processingResult.UniqueSkuCount} unique SKUs in {processingResult.ValidBoxes} boxes.", true);
                Logger.LogInformation("Data processing successful. {ValidBoxes} valid boxes, {UniqueSkus} unique SKUs",
                    processingResult.ValidBoxes, processingResult.UniqueSkuCount);
            }
            else
            {
                var errorMessage = string.Join("; ", updatedResult.Errors);
                SetStatusMessage($"Data processing failed: {errorMessage}", false);
                Logger.LogWarning("Data processing failed. Errors: {Errors}", errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error processing extracted data: {Error}", ex.Message);
            SetStatusMessage($"Data processing error: {ex.Message}", false);
        }
        finally
        {
            isProcessing = false;
            currentOperation = "";
            StateHasChanged();
        }
    }

    private async Task ProcessAndCreateShipment()
    {
        if (selectedImageFile == null)
        {
            SetStatusMessage("Please select an image file first.", false);
            return;
        }

        if (!isDataProcessed)
        {
            SetStatusMessage("Please process the image data first before creating shipment.", false);
            return;
        }

        try
        {
            isProcessing = true;
            currentOperation = "create";
            SetStatusMessage("Starting shipment creation...", true);

            Logger.LogInformation("Starting shipment creation for file: {FileName}", selectedImageFile.Name);

            var formFile = new BrowserFileAdapter(selectedImageFile);
            var request = new ImageToShipmentRequest
            {
                ImageFile = formFile,
                ShipmentName = shipmentName,
                FulfillmentCenterId = selectedFulfillmentCenter,
                AutoCreateShipment = true,
                ValidateOnly = false
            };

            processingResult = await ImageToShipmentService.ProcessImageAndCreateShipmentAsync(request);

            if (processingResult.IsSuccess && !string.IsNullOrEmpty(processingResult.ShipmentId))
            {
                SetStatusMessage($"Shipment created successfully! ID: {processingResult.ShipmentId}", true);
                Logger.LogInformation("Shipment created successfully for file: {FileName}. Shipment ID: {ShipmentId}", 
                    selectedImageFile.Name, processingResult.ShipmentId);

                // Reset form
                selectedImageFile = null;
                shipmentName = "";
                isValidated = false;
                isDataProcessed = false;

                // Note: Statistics refresh removed as part of cleanup
            }
            else
            {
                var errorMessage = string.Join("; ", processingResult.Errors);
                SetStatusMessage($"Shipment creation failed: {errorMessage}", false);
                Logger.LogWarning("Shipment creation failed for file: {FileName}. Errors: {Errors}", 
                    selectedImageFile.Name, errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating shipment for file: {FileName}", selectedImageFile?.Name ?? "Unknown");
            SetStatusMessage($"Shipment creation error: {ex.Message}", false);
        }
        finally
        {
            isProcessing = false;
            currentOperation = "";
            StateHasChanged();
        }
    }

    // Note: Removed unused methods (RefreshMapping, ShowSampleImage, ShowStatistics) as part of cleanup

    // Note: Statistics loading methods removed as part of cleanup

    private void SetStatusMessage(string message, bool success)
    {
        statusMessage = message;
        isSuccess = success;
    }

    private void ClearStatus()
    {
        statusMessage = "";
    }

    private static string FormatFileSize(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
        return $"{bytes / (1024.0 * 1024.0):F1} MB";
    }

    // Note: CalculateSuccessRate method removed as part of cleanup

    private async Task OnJsonDataSaved(List<BoxDataRow> updatedData)
    {
        if (processingResult != null)
        {
            processingResult.ExtractedBoxData = updatedData;
            processingResult.JsonEdited = true;
            SetStatusMessage("JSON data saved successfully. You can now process the data.", true);
            Logger.LogInformation("JSON data saved with {BoxCount} boxes", updatedData.Count);
        }
        await Task.CompletedTask;
    }

    private async Task OnJsonDataChanged()
    {
        // Mark that data has been changed
        if (processingResult != null)
        {
            processingResult.JsonEdited = true;
        }
        await Task.CompletedTask;
    }

    // ...existing methods...
}
