@page "/image-shipment"
@rendermode @(InteractiveServer)
@using SilvrBear_Amazon_Automation.Models
@using SilvrBear_Amazon_Automation.Services
@using SilvrBear_Amazon_Automation.Constants
@using SilvrBear_Amazon_Automation.Helpers
@inject IImageToShipmentService ImageToShipmentService
@inject IProductMappingService ProductMappingService
@inject ILogger<ImageShipmentProcessor> Logger
@inject IJSRuntime JSRuntime

<PageTitle>Image to Shipment Processor</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>
                    <i class="bi bi-camera"></i> Image to Shipment Processor
                </h2>
                <div>
                    <button class="btn btn-outline-info me-2" @onclick="ShowSampleImage">
                        <i class="bi bi-image"></i> View Sample
                    </button>
                    <button class="btn btn-outline-secondary me-2" @onclick="RefreshMapping">
                        <i class="bi bi-arrow-clockwise"></i> Refresh Mapping
                    </button>
                    <button class="btn btn-info" @onclick="ShowStatistics">
                        <i class="bi bi-graph-up"></i> Statistics
                    </button>
                </div>
            </div>
        </div>
    </div>

    @if (!string.IsNullOrEmpty(statusMessage))
    {
        <div class="alert @(isSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
            @statusMessage
            <button type="button" class="btn-close" @onclick="ClearStatus"></button>
        </div>
    }

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-upload"></i> Upload Shipment Image
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <label class="form-label">Shipment Name</label>
                                <input type="text" class="form-control" @bind="shipmentName" 
                                       placeholder="Enter shipment name (optional)" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Fulfillment Center</label>
                                <select class="form-select" @bind="selectedFulfillmentCenter">
                                    @foreach (var fc in ShipmentConstants.FulfillmentCenters.IndiaFulfillmentCenters)
                                    {
                                        <option value="@fc.Key" selected="@(fc.Key == ShipmentConstants.FulfillmentCenters.DefaultFulfillmentCenterId)">
                                            @fc.Value
                                        </option>
                                    }
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label">Image File</label>
                        <InputFile OnChange="HandleImageSelection" class="form-control" accept="image/*" />
                        <div class="form-text">
                            Supported formats: JPG, PNG, BMP, GIF. Maximum size: @(ShipmentConstants.FileProcessing.MaxImageSizeMB)MB
                        </div>
                    </div>

                    @if (selectedImageFile != null)
                    {
                        <div class="mb-3">
                            <div class="card bg-light">
                                <div class="card-body">
                                    <h6 class="card-title">Selected Image</h6>
                                    <p class="card-text">
                                        <strong>File:</strong> @selectedImageFile.Name<br />
                                        <strong>Size:</strong> @FormatFileSize(selectedImageFile.Size)<br />
                                        <strong>Type:</strong> @selectedImageFile.ContentType
                                    </p>
                                    <div class="small text-muted mt-2">
                                        <strong>Debug Info:</strong><br />
                                        selectedImageFile != null: @(selectedImageFile != null)<br />
                                        isProcessing: @isProcessing<br />
                                        isValidated: @isValidated<br />
                                        Validate Button Enabled: @IsValidateButtonEnabled()<br />
                                        Process Button Enabled: @IsProcessButtonEnabled()
                                    </div>
                                </div>
                            </div>
                        </div>
                    }

                    <div class="d-flex gap-2">
                        <button type="button" class="btn btn-warning" @onclick="TestButtonClick">
                            <i class="bi bi-bug"></i> Test Click
                        </button>
                        
                        <button type="button" class="btn btn-success" @onclick="DebugValidateImage" 
                                disabled="@(!IsValidateButtonEnabled())">
                            @if (isProcessing && currentOperation == "validate")
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            <i class="bi bi-check-circle"></i> Validate Image
                        </button>
                        
                        <button type="button" class="btn btn-primary" @onclick="DebugProcessAndCreateShipment" 
                                disabled="@(!IsProcessButtonEnabled())">
                            @if (isProcessing && currentOperation == "create")
                            {
                                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                            }
                            <i class="bi bi-box-arrow-up"></i> Process & Create Shipment
                        </button>
                    </div>

                    @if (isProcessing)
                    {
                        <div class="mt-3">
                            <div class="progress">
                                <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                     role="progressbar" style="width: 100%">
                                    Processing @currentOperation...
                                </div>
                            </div>
                        </div>
                    }
                </div>
            </div>

            @if (processingResult != null && processingResult.ImageProcessingResult != null)
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-list-check"></i> Extracted Box Data
                        </h5>
                    </div>
                    <div class="card-body">
                        @if (processingResult.ImageProcessingResult.ProcessedBoxes.Any())
                        {
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Box #</th>
                                            <th>Original Product</th>
                                            <th>Mapped Product</th>
                                            <th>SKU</th>
                                            <th>Qty</th>
                                            <th>Dimensions</th>
                                            <th>Weight (kg)</th>
                                            <th>Status</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var box in processingResult.ImageProcessingResult.ProcessedBoxes)
                                        {
                                            <tr class="@(box.IsValid ? "" : "table-warning")">
                                                <td>@box.BoxNumber</td>
                                                <td>@box.OriginalProductName</td>
                                                <td>@box.MappedProductName</td>
                                                <td><code>@box.GeneratedSKU</code></td>
                                                <td>@box.Quantity</td>
                                                <td>@box.DimensionsString</td>
                                                <td>@box.TotalBoxWeightKg.ToString("F2")</td>
                                                <td>
                                                    @if (box.IsValid)
                                                    {
                                                        <span class="badge bg-success">Valid</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="badge bg-warning">Issues</span>
                                                    }
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        }
                        else
                        {
                            <div class="text-center text-muted py-4">
                                <i class="bi bi-exclamation-circle display-4"></i>
                                <p class="mt-2">No boxes extracted from the image</p>
                            </div>
                        }
                    </div>
                </div>
            }
        </div>

        <div class="col-lg-4">
            @if (mappingStats != null)
            {
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-database"></i> Product Mapping Status
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small">
                            <li><strong>Total Products:</strong> @mappingStats.TotalProducts</li>
                            <li><strong>Active Products:</strong> @mappingStats.ActiveProducts</li>
                            <li><strong>Categories:</strong> @mappingStats.UniqueCategories</li>
                            <li><strong>Last Updated:</strong> @mappingStats.LastUpdated.ToString("yyyy-MM-dd HH:mm")</li>
                        </ul>
                    </div>
                </div>
            }

            @if (processingResult != null)
            {
                <div class="card mb-3">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-info-circle"></i> Processing Result
                        </h6>
                    </div>
                    <div class="card-body">
                        @if (processingResult.IsSuccess)
                        {
                            <div class="text-success mb-2">
                                <i class="bi bi-check-circle"></i> Processing successful
                            </div>
                            
                            @if (!string.IsNullOrEmpty(processingResult.ShipmentId))
                            {
                                <div class="alert alert-success">
                                    <strong>Shipment Created!</strong><br />
                                    <strong>ID:</strong> @processingResult.ShipmentId<br />
                                    <strong>Time:</strong> @processingResult.ProcessedAt.ToString("HH:mm:ss")
                                </div>
                            }

                            <ul class="list-unstyled small">
                                <li><strong>Total Boxes:</strong> @processingResult.TotalBoxesProcessed</li>
                                <li><strong>Valid Boxes:</strong> @processingResult.ValidBoxes</li>
                                <li><strong>Invalid Boxes:</strong> @processingResult.InvalidBoxes</li>
                                <li><strong>Processing Time:</strong> @processingResult.TotalProcessingTime.TotalSeconds.ToString("F1")s</li>
                            </ul>
                        }
                        else
                        {
                            <div class="text-danger mb-2">
                                <i class="bi bi-exclamation-circle"></i> Processing failed
                            </div>
                        }

                        @if (processingResult.Errors.Any())
                        {
                            <div class="mt-2">
                                <strong class="text-danger">Errors:</strong>
                                <ul class="list-unstyled small">
                                    @foreach (var error in processingResult.Errors)
                                    {
                                        <li class="text-danger">• @error</li>
                                    }
                                </ul>
                            </div>
                        }

                        @if (processingResult.Warnings.Any())
                        {
                            <div class="mt-2">
                                <strong class="text-warning">Warnings:</strong>
                                <ul class="list-unstyled small">
                                    @foreach (var warning in processingResult.Warnings)
                                    {
                                        <li class="text-warning">• @warning</li>
                                    }
                                </ul>
                            </div>
                        }
                    </div>
                </div>
            }

            @if (processingStatistics != null)
            {
                <div class="card">
                    <div class="card-header">
                        <h6 class="card-title mb-0">
                            <i class="bi bi-graph-up"></i> Processing Statistics
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled small">
                            <li><strong>Images Processed:</strong> @processingStatistics.TotalImagesProcessed</li>
                            <li><strong>Success Rate:</strong> @CalculateSuccessRate()%</li>
                            <li><strong>Shipments Created:</strong> @processingStatistics.TotalShipmentsCreated</li>
                            <li><strong>Avg Processing Time:</strong> @processingStatistics.AverageProcessingTime.TotalSeconds.ToString("F1")s</li>
                            @if (processingStatistics.LastProcessedAt != default)
                            {
                                <li><strong>Last Processed:</strong> @processingStatistics.LastProcessedAt.ToString("yyyy-MM-dd HH:mm")</li>
                            }
                        </ul>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private IBrowserFile? selectedImageFile;
    private string shipmentName = "";
    private string selectedFulfillmentCenter = ShipmentConstants.FulfillmentCenters.DefaultFulfillmentCenterId;
    private string statusMessage = "";
    private bool isSuccess = false;
    private bool isProcessing = false;
    private bool isValidated = false;
    private string currentOperation = "";

    private ImageToShipmentResult? processingResult;
    private ProductMappingStatistics? mappingStats;
    private ImageProcessingStatistics? processingStatistics;

    protected override async Task OnInitializedAsync()
    {
        try
        {
            await LoadMappingStatistics();
            await LoadProcessingStatistics();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error initializing component");
            SetStatusMessage("Error loading initial data", false);
        }
    }

    private void HandleImageSelection(InputFileChangeEventArgs e)
    {
        selectedImageFile = e.File;
        isValidated = false;
        processingResult = null;

        if (selectedImageFile != null)
        {
            SetStatusMessage($"Image selected: {selectedImageFile.Name}", true);
            Logger.LogInformation("Image file selected: {FileName}, Size: {Size}", 
                selectedImageFile.Name, selectedImageFile.Size);
        }
        else
        {
            SetStatusMessage("No image selected", false);
            Logger.LogInformation("No image file selected");
        }
        
        // Force UI update
        StateHasChanged();
    }

    private async Task ValidateImage()
    {
        if (selectedImageFile == null) 
        {
            SetStatusMessage("Please select an image file first.", false);
            return;
        }

        try
        {
            isProcessing = true;
            currentOperation = "validate";
            SetStatusMessage("Starting image validation...", true);

            Logger.LogInformation("Starting image validation for file: {FileName}", selectedImageFile.Name);

            var formFile = new BrowserFileAdapter(selectedImageFile);
            processingResult = await ImageToShipmentService.ValidateImageDataAsync(
                formFile,
                shipmentName,
                selectedFulfillmentCenter);

            if (processingResult.IsSuccess)
            {
                isValidated = true;
                SetStatusMessage($"Image validation successful! Found {processingResult.ValidBoxes} valid boxes.", true);
                Logger.LogInformation("Image validation successful for file: {FileName}", selectedImageFile.Name);
            }
            else
            {
                var errorMessage = string.Join("; ", processingResult.Errors);
                SetStatusMessage($"Validation failed: {errorMessage}", false);
                Logger.LogWarning("Image validation failed for file: {FileName}. Errors: {Errors}", 
                    selectedImageFile.Name, errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error validating image: {FileName}", selectedImageFile?.Name ?? "Unknown");
            SetStatusMessage($"Validation error: {ex.Message}", false);
        }
        finally
        {
            isProcessing = false;
            currentOperation = "";
            StateHasChanged();
        }
    }

    private async Task ProcessAndCreateShipment()
    {
        if (selectedImageFile == null)
        {
            SetStatusMessage("Please select an image file first.", false);
            return;
        }

        if (!isValidated)
        {
            SetStatusMessage("Please validate the image first before creating shipment.", false);
            return;
        }

        try
        {
            isProcessing = true;
            currentOperation = "create";
            SetStatusMessage("Starting shipment creation...", true);

            Logger.LogInformation("Starting shipment creation for file: {FileName}", selectedImageFile.Name);

            var formFile = new BrowserFileAdapter(selectedImageFile);
            var request = new ImageToShipmentRequest
            {
                ImageFile = formFile,
                ShipmentName = shipmentName,
                FulfillmentCenterId = selectedFulfillmentCenter,
                AutoCreateShipment = true,
                ValidateOnly = false
            };

            processingResult = await ImageToShipmentService.ProcessImageAndCreateShipmentAsync(request);

            if (processingResult.IsSuccess && !string.IsNullOrEmpty(processingResult.ShipmentId))
            {
                SetStatusMessage($"Shipment created successfully! ID: {processingResult.ShipmentId}", true);
                Logger.LogInformation("Shipment created successfully for file: {FileName}. Shipment ID: {ShipmentId}", 
                    selectedImageFile.Name, processingResult.ShipmentId);

                // Reset form
                selectedImageFile = null;
                shipmentName = "";
                isValidated = false;

                // Refresh statistics
                await LoadProcessingStatistics();
            }
            else
            {
                var errorMessage = string.Join("; ", processingResult.Errors);
                SetStatusMessage($"Shipment creation failed: {errorMessage}", false);
                Logger.LogWarning("Shipment creation failed for file: {FileName}. Errors: {Errors}", 
                    selectedImageFile.Name, errorMessage);
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error creating shipment for file: {FileName}", selectedImageFile?.Name ?? "Unknown");
            SetStatusMessage($"Shipment creation error: {ex.Message}", false);
        }
        finally
        {
            isProcessing = false;
            currentOperation = "";
            StateHasChanged();
        }
    }

    private async Task RefreshMapping()
    {
        try
        {
            await ProductMappingService.RefreshMappingAsync();
            await LoadMappingStatistics();
            SetStatusMessage("Product mapping refreshed successfully", true);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error refreshing mapping");
            SetStatusMessage($"Error refreshing mapping: {ex.Message}", false);
        }
    }

    private async Task ShowSampleImage()
    {
        try
        {
            await JSRuntime.InvokeVoidAsync("window.open", "/Data/sample-image.jpg", "_blank");
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error showing sample image");
            SetStatusMessage("Error opening sample image", false);
        }
    }

    private async Task ShowStatistics()
    {
        await LoadProcessingStatistics();
        SetStatusMessage("Statistics refreshed", true);
    }

    private async Task LoadMappingStatistics()
    {
        try
        {
            mappingStats = await ProductMappingService.GetMappingStatisticsAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading mapping statistics");
        }
    }

    private async Task LoadProcessingStatistics()
    {
        try
        {
            processingStatistics = await ImageToShipmentService.GetProcessingStatisticsAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading processing statistics");
        }
    }

    private void SetStatusMessage(string message, bool success)
    {
        statusMessage = message;
        isSuccess = success;
    }

    private void ClearStatus()
    {
        statusMessage = "";
    }

    private static string FormatFileSize(long bytes)
    {
        if (bytes < 1024) return $"{bytes} B";
        if (bytes < 1024 * 1024) return $"{bytes / 1024.0:F1} KB";
        return $"{bytes / (1024.0 * 1024.0):F1} MB";
    }

    private string CalculateSuccessRate()
    {
        if (processingStatistics == null || processingStatistics.TotalImagesProcessed == 0)
            return "0";

        var rate = (double)processingStatistics.SuccessfulProcessing / processingStatistics.TotalImagesProcessed * 100;
        return rate.ToString("F1");
    }

    // Helper methods for button state
    private bool IsValidateButtonEnabled()
    {
        var enabled = selectedImageFile != null && !isProcessing;
        Logger.LogDebug("IsValidateButtonEnabled: {Enabled} (selectedImageFile: {HasFile}, isProcessing: {IsProcessing})", 
            enabled, selectedImageFile != null, isProcessing);
        return enabled;
    }

    private bool IsProcessButtonEnabled()
    {
        var enabled = selectedImageFile != null && !isProcessing && isValidated;
        Logger.LogDebug("IsProcessButtonEnabled: {Enabled} (selectedImageFile: {HasFile}, isProcessing: {IsProcessing}, isValidated: {IsValidated})", 
            enabled, selectedImageFile != null, isProcessing, isValidated);
        return enabled;
    }

    // Debug methods
    private async Task DebugValidateImage()
    {
        Logger.LogInformation("DebugValidateImage called - selectedImageFile: {HasFile}, isProcessing: {IsProcessing}", 
            selectedImageFile != null, isProcessing);
        await JSRuntime.InvokeVoidAsync("console.log", "Validate Image button clicked");
        await ValidateImage();
    }

    private async Task DebugProcessAndCreateShipment()
    {
        Logger.LogInformation("DebugProcessAndCreateShipment called - selectedImageFile: {HasFile}, isValidated: {IsValidated}, isProcessing: {IsProcessing}", 
            selectedImageFile != null, isValidated, isProcessing);
        await JSRuntime.InvokeVoidAsync("console.log", "Process & Create Shipment button clicked");
        await ProcessAndCreateShipment();
    }

    private async Task TestButtonClick()
    {
        var hasFile = selectedImageFile != null;
        var fileName = selectedImageFile?.Name ?? "No file";
        var validateEnabled = IsValidateButtonEnabled();
        var processEnabled = IsProcessButtonEnabled();
        
        var debugInfo = "Debug State Information:\n" +
                       $"- selectedImageFile != null: {hasFile}\n" +
                       $"- selectedImageFile Name: {fileName}\n" +
                       $"- isProcessing: {isProcessing}\n" +
                       $"- isValidated: {isValidated}\n" +
                       $"- currentOperation: {currentOperation}\n\n" +
                       "Button States (using helper methods):\n" +
                       $"- Validate Button Enabled: {validateEnabled}\n" +
                       $"- Process Button Enabled: {processEnabled}\n\n" +
                       "Component Render Mode: InteractiveServer";
        
        await JSRuntime.InvokeVoidAsync("alert", debugInfo);
        SetStatusMessage("Test button clicked successfully! Check debug info above.", true);
        Logger.LogInformation("Test button clicked. File selected: {HasFile}, Name: {FileName}, ValidateEnabled: {ValidateEnabled}", 
            hasFile, fileName, validateEnabled);
    }
}
