using System.Diagnostics;
using System.Text.Json;
using System.Text.RegularExpressions;
using SilvrBear_Amazon_Automation.Constants;
using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Service for complete image to shipment processing workflow
/// </summary>
public class ImageToShipmentService : IImageToShipmentService
{
    private readonly IOpenAIImageService _openAIService;
    private readonly IProductMappingService _productMappingService;
    private readonly IInboundShipmentService _shipmentService;
    private readonly ILogger<ImageToShipmentService> _logger;
    private readonly ImageProcessingStatistics _statistics = new();

    public ImageToShipmentService(
        IOpenAIImageService openAIService,
        IProductMappingService productMappingService,
        IInboundShipmentService shipmentService,
        ILogger<ImageToShipmentService> logger)
    {
        _openAIService = openAIService;
        _productMappingService = productMappingService;
        _shipmentService = shipmentService;
        _logger = logger;
    }

    /// <summary>
    /// Processes an image and creates a complete inbound shipment
    /// </summary>
    public async Task<ImageToShipmentResult> ProcessImageAndCreateShipmentAsync(ImageToShipmentRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new ImageToShipmentResult();

        try
        {
            _logger.LogInformation("Starting complete image to shipment processing for: {FileName}", request.ImageFile.FileName);
            _statistics.TotalImagesProcessed++;

            // Step 1: Process image with OpenAI
            var availableProducts = await _productMappingService.GetAvailableProductNamesAsync();
            var imageResult = await _openAIService.ProcessImageAsync(request.ImageFile, availableProducts);
            result.ImageProcessingResult = imageResult;

            if (!imageResult.IsSuccess)
            {
                result.Errors.AddRange(imageResult.Errors);
                _statistics.FailedProcessing++;
                return result;
            }

            // Step 2: Map and enrich box data
            var enrichedBoxes = await MapAndEnrichBoxDataAsync(imageResult.ProcessedBoxes);
            result.TotalBoxesProcessed = enrichedBoxes.Count;
            result.ValidBoxes = enrichedBoxes.Count(b => b.IsValid);
            result.InvalidBoxes = enrichedBoxes.Count(b => !b.IsValid);

            if (result.ValidBoxes == 0)
            {
                result.Errors.Add("No valid boxes found after processing and mapping");
                _statistics.FailedProcessing++;
                return result;
            }

            // Step 2.5: Create consolidated item and box details
            var validBoxes = enrichedBoxes.Where(b => b.IsValid).ToList();
            result.ConsolidatedItems = CreateConsolidatedItemDetails(validBoxes);
            result.ConsolidatedBoxes = CreateConsolidatedBoxDetails(validBoxes);
            result.UniqueSkuCount = result.ConsolidatedItems.Count;
            result.TotalShipmentWeightKg = result.ConsolidatedBoxes.Sum(b => b.TotalWeightKg);

            // Step 3: Convert to shipment request
            var shipmentName = request.ShipmentName ?? $"IMG-Shipment-{DateTime.UtcNow:yyyyMMdd-HHmmss}";
            var fulfillmentCenterId = request.FulfillmentCenterId ?? ShipmentConstants.FulfillmentCenters.DefaultFulfillmentCenterId;

            var shipmentRequest = await ConvertToShipmentRequestAsync(enrichedBoxes.Where(b => b.IsValid).ToList(), shipmentName, fulfillmentCenterId);

            // Step 4: Validate shipment data
            var (isValid, validationErrors, validationWarnings) = await ValidateShipmentDataAsync(shipmentRequest);
            result.Warnings.AddRange(validationWarnings);

            if (!isValid)
            {
                result.Errors.AddRange(validationErrors);
                _statistics.FailedProcessing++;
                return result;
            }

            // Step 5: Create shipment if requested
            if (request.AutoCreateShipment && !request.ValidateOnly)
            {
                var shipmentResponse = await _shipmentService.CreateCompleteInboundShipmentAsync(shipmentRequest);
                result.ShipmentResponse = shipmentResponse;

                if (shipmentResponse.Errors.Any())
                {
                    result.Errors.AddRange(shipmentResponse.Errors);
                    _statistics.FailedProcessing++;
                    return result;
                }

                result.ShipmentId = shipmentResponse.ShipmentId;
                _statistics.TotalShipmentsCreated++;
                _logger.LogInformation("Successfully created shipment: {ShipmentId}", result.ShipmentId);
            }

            result.IsSuccess = true;
            _statistics.SuccessfulProcessing++;
            _statistics.TotalBoxesExtracted += result.ValidBoxes;

            // Update product statistics
            foreach (var box in enrichedBoxes.Where(b => b.IsValid))
            {
                var productKey = box.MappedProductName;
                _statistics.ProductCounts[productKey] = _statistics.ProductCounts.GetValueOrDefault(productKey, 0) + 1;
            }

            _logger.LogInformation("Image to shipment processing completed successfully. Boxes: {ValidBoxes}/{TotalBoxes}", 
                result.ValidBoxes, result.TotalBoxesProcessed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in image to shipment processing: {Error}", ex.Message);
            result.Errors.Add($"Processing error: {ex.Message}");
            _statistics.FailedProcessing++;

            var errorKey = ex.GetType().Name;
            _statistics.ErrorCounts[errorKey] = _statistics.ErrorCounts.GetValueOrDefault(errorKey, 0) + 1;
        }
        finally
        {
            stopwatch.Stop();
            result.TotalProcessingTime = stopwatch.Elapsed;
            _statistics.LastProcessedAt = DateTime.UtcNow;
            
            // Update average processing time
            var totalTime = _statistics.AverageProcessingTime.TotalMilliseconds * (_statistics.TotalImagesProcessed - 1) + 
                           stopwatch.Elapsed.TotalMilliseconds;
            _statistics.AverageProcessingTime = TimeSpan.FromMilliseconds(totalTime / _statistics.TotalImagesProcessed);
        }

        return result;
    }

    /// <summary>
    /// Extracts JSON data from image without processing or mapping
    /// </summary>
    public async Task<ImageToShipmentResult> ExtractJsonFromImageAsync(
        IFormFile imageFile,
        string? shipmentName = null,
        string? fulfillmentCenterId = null)
    {
        var result = new ImageToShipmentResult();
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting JSON extraction from image: {FileName}", imageFile.FileName);

            // Check if we should use mock data (development mode)
            var useMockData = true; // This should match the OpenAI service setting

            if (useMockData)
            {
                // Step 1: Load and parse mock JSON data directly
                _logger.LogInformation("Using mock data for JSON extraction");
                var mockDataPath = Path.Combine(Directory.GetCurrentDirectory(), "Data", "data.json.txt");

                if (!File.Exists(mockDataPath))
                {
                    result.Errors.Add($"Mock data file not found: {mockDataPath}");
                    return result;
                }

                var mockDataContent = await File.ReadAllTextAsync(mockDataPath);
                mockDataContent = mockDataContent.Replace("```json", "").Replace("```", "").Trim();

                // Parse the JSON directly to BoxDataRow format
                var mockData = System.Text.Json.JsonSerializer.Deserialize<OpenAIImageResponse>(mockDataContent, new JsonSerializerOptions
                {
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                    PropertyNameCaseInsensitive = true
                });

                if (mockData?.Boxes == null || !mockData.Boxes.Any())
                {
                    result.Errors.Add("No box data found in mock JSON");
                    return result;
                }

                // Step 2: Convert directly to BoxDataRow format
                result.ExtractedBoxData = mockData.Boxes
                    .Select(box => new BoxDataRow
                    {
                        BoxNumber = box.BoxNumber,
                        ProductName = box.ProductName,
                        BarcodeType = box.BarcodeType,
                        Quantity = box.Quantity,
                        Dimensions = box.Dimensions,
                        Remarks = box.Remarks
                    })
                    .ToList();
            }
            else
            {
                // Step 1: Process image with OpenAI to get raw JSON
                var availableProducts = await _productMappingService.GetAvailableProductNamesAsync();
                var imageResult = await _openAIService.ProcessImageAsync(imageFile, availableProducts);

                if (!imageResult.IsSuccess)
                {
                    result.Errors.AddRange(imageResult.Errors);
                    return result;
                }

                // Step 2: Convert to editable BoxDataRow format
                result.ExtractedBoxData = imageResult.ProcessedBoxes
                    .Select(box => new BoxDataRow
                    {
                        BoxNumber = box.BoxNumber,
                        ProductName = box.OriginalProductName,
                        BarcodeType = box.BarcodeType,
                        Quantity = box.Quantity,
                        Dimensions = box.DimensionsString,
                        Remarks = box.Remarks
                    })
                    .ToList();
            }

            // Step 3: Validate extracted data
            foreach (var boxData in result.ExtractedBoxData)
            {
                boxData.Validate();
            }

            result.JsonExtracted = true;
            result.TotalBoxesProcessed = result.ExtractedBoxData.Count;
            result.ValidBoxes = result.ExtractedBoxData.Count(b => b.IsValid);
            result.InvalidBoxes = result.ExtractedBoxData.Count(b => !b.IsValid);
            result.IsSuccess = result.ExtractedBoxData.Any();

            if (!result.IsSuccess)
            {
                result.Errors.Add("No box data extracted from image");
            }

            _logger.LogInformation("JSON extraction completed. Found {BoxCount} boxes", result.ExtractedBoxData.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting JSON from image: {Error}", ex.Message);
            result.Errors.Add($"JSON extraction error: {ex.Message}");
        }
        finally
        {
            stopwatch.Stop();
            result.TotalProcessingTime = stopwatch.Elapsed;
            result.ProcessedAt = DateTime.UtcNow;
        }

        return result;
    }

    /// <summary>
    /// Processes extracted JSON data and maps to products
    /// </summary>
    public async Task<ImageToShipmentResult> ProcessExtractedDataAsync(
        List<BoxDataRow> extractedBoxData,
        string? shipmentName = null,
        string? fulfillmentCenterId = null)
    {
        var result = new ImageToShipmentResult
        {
            ExtractedBoxData = extractedBoxData,
            JsonExtracted = true
        };
        var stopwatch = Stopwatch.StartNew();

        try
        {
            _logger.LogInformation("Starting processing of extracted data with {BoxCount} boxes", extractedBoxData.Count);

            // Step 1: Convert BoxDataRow back to ProcessedBoxData for processing
            var processedBoxes = extractedBoxData.Select(boxRow => new ProcessedBoxData
            {
                BoxNumber = boxRow.BoxNumber,
                OriginalProductName = boxRow.ProductName,
                BarcodeType = boxRow.BarcodeType,
                Quantity = boxRow.Quantity,
                DimensionsString = boxRow.Dimensions,
                Remarks = boxRow.Remarks
            }).ToList();

            // Step 2: Map and enrich box data
            var enrichedBoxes = await MapAndEnrichBoxDataAsync(processedBoxes);
            result.TotalBoxesProcessed = enrichedBoxes.Count;
            result.ValidBoxes = enrichedBoxes.Count(b => b.IsValid);
            result.InvalidBoxes = enrichedBoxes.Count(b => !b.IsValid);

            if (result.ValidBoxes == 0)
            {
                result.Errors.Add("No valid boxes found after processing and mapping");
                return result;
            }

            // Step 3: Create consolidated item and box details
            var validBoxes = enrichedBoxes.Where(b => b.IsValid).ToList();
            result.ConsolidatedItems = CreateConsolidatedItemDetails(validBoxes);
            result.ConsolidatedBoxes = CreateConsolidatedBoxDetails(validBoxes);
            result.UniqueSkuCount = result.ConsolidatedItems.Count;
            result.TotalShipmentWeightKg = result.ConsolidatedBoxes.Sum(b => b.TotalWeightKg);

            // Step 4: Create image processing result for display
            result.ImageProcessingResult = new ImageProcessingResult
            {
                IsSuccess = true,
                ProcessedBoxes = enrichedBoxes,
                ProcessedAt = DateTime.UtcNow,
                ProcessingTime = stopwatch.Elapsed
            };

            result.DataProcessed = true;
            result.IsSuccess = true;

            _logger.LogInformation("Data processing completed. {ValidBoxes} valid boxes, {UniqueSkus} unique SKUs",
                result.ValidBoxes, result.UniqueSkuCount);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing extracted data: {Error}", ex.Message);
            result.Errors.Add($"Data processing error: {ex.Message}");
        }
        finally
        {
            stopwatch.Stop();
            result.TotalProcessingTime = stopwatch.Elapsed;
            result.ProcessedAt = DateTime.UtcNow;
        }

        return result;
    }

    /// <summary>
    /// Processes an image and validates data without creating shipment
    /// </summary>
    public async Task<ImageToShipmentResult> ValidateImageDataAsync(
        IFormFile imageFile,
        string? shipmentName = null,
        string? fulfillmentCenterId = null)
    {
        var request = new ImageToShipmentRequest
        {
            ImageFile = imageFile,
            ShipmentName = shipmentName,
            FulfillmentCenterId = fulfillmentCenterId,
            AutoCreateShipment = false,
            ValidateOnly = true
        };

        return await ProcessImageAndCreateShipmentAsync(request);
    }

    /// <summary>
    /// Processes extracted box data and maps to products
    /// </summary>
    public async Task<List<ProcessedBoxData>> MapAndEnrichBoxDataAsync(List<ProcessedBoxData> processedBoxes)
    {
        var enrichedBoxes = new List<ProcessedBoxData>();

        foreach (var box in processedBoxes)
        {
            try
            {
                // Find best matching product
                var mappedProduct = await _productMappingService.FindBestMatchAsync(box.OriginalProductName, box.BarcodeType);
                
                _logger.LogInformation("DEBUG: Looking for product '{ProductName}' with barcode '{BarcodeType}'", 
                    box.OriginalProductName, box.BarcodeType ?? "null");
                
                if (mappedProduct != null)
                {
                    box.MappedProductName = mappedProduct.ProductName;
                    box.GeneratedSKU = await _productMappingService.GetSKUAsync(mappedProduct.ProductName, box.BarcodeType);
                    box.ItemWeightKg = mappedProduct.WeightKg;

                    _logger.LogInformation("Mapped product found: '{ProductName}' -> '{MappedName}' SKU: '{SKU}', Weight: {Weight} kg",
                        box.OriginalProductName, box.MappedProductName, box.GeneratedSKU, box.ItemWeightKg);
                }
                else
                {
                    box.MappedProductName = box.OriginalProductName;
                    box.GeneratedSKU = await _productMappingService.GetSKUAsync(box.OriginalProductName, box.BarcodeType);
                    box.ItemWeightKg = await _productMappingService.GetProductWeightAsync(box.OriginalProductName, box.BarcodeType);
                    box.ValidationWarnings.Add($"Product '{box.OriginalProductName}' not found in mapping. Using default values.");

                    _logger.LogWarning("Product not found in mapping: '{ProductName}' -> Using default SKU: '{SKU}', Weight: {Weight} kg",
                        box.OriginalProductName, box.GeneratedSKU, box.ItemWeightKg);
                }

                // Calculate metrics
                var calculatedBox = await CalculateBoxMetricsAsync(box);
                box.TotalItemWeightKg = calculatedBox.TotalItemWeightKg;
                box.BoxWeightKg = calculatedBox.BoxWeightKg;
                box.TotalBoxWeightKg = calculatedBox.TotalBoxWeightKg;
                
                // Parse dimensions
                box.ParsedDimensions = ParseDimensions(box.DimensionsString);

                enrichedBoxes.Add(box);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enriching box {BoxNumber}: {Error}", box.BoxNumber, ex.Message);
                box.ValidationErrors.Add($"Error processing box: {ex.Message}");
                box.IsValid = false;
                enrichedBoxes.Add(box);
            }
        }

        return enrichedBoxes;
    }

    /// <summary>
    /// Converts processed box data to shipment request
    /// </summary>
    public Task<CreateInboundShipmentRequest> ConvertToShipmentRequestAsync(
        List<ProcessedBoxData> processedBoxes,
        string shipmentName,
        string fulfillmentCenterId)
    {
        var shipmentItems = new List<ShipmentItem>();
        var shipmentBoxes = new List<ShipmentBox>();

        foreach (var box in processedBoxes.Where(b => b.IsValid))
        {
            // Create shipment item
            var shipmentItem = new ShipmentItem
            {
                Sku = box.GeneratedSKU,
                Quantity = box.Quantity,
                ProductName = box.MappedProductName,
                Condition = "NewItem",
                UnitCost = 0 // Will be set later if needed
            };
            shipmentItems.Add(shipmentItem);

            // Create shipment box
            var shipmentBox = new ShipmentBox
            {
                BoxId = $"BOX-{box.BoxNumber:D3}",
                Dimensions = new BoxDimensions
                {
                    Length = box.ParsedDimensions.Length,
                    Width = box.ParsedDimensions.Width,
                    Height = box.ParsedDimensions.Height,
                    Unit = "inches" // Convert to inches for Amazon API
                },
                Weight = new BoxWeight
                {
                    Value = box.TotalBoxWeightKg * ShipmentConstants.Conversions.KgToPounds, // Convert to pounds
                    Unit = "pounds"
                },
                Contents = new List<BoxContent>
                {
                    new()
                    {
                        Sku = box.GeneratedSKU,
                        Quantity = box.Quantity
                    }
                }
            };
            shipmentBoxes.Add(shipmentBox);
        }

        return Task.FromResult(new CreateInboundShipmentRequest
        {
            ShipmentName = shipmentName,
            DestinationFulfillmentCenterId = fulfillmentCenterId,
            Items = shipmentItems,
            Boxes = shipmentBoxes,
            LabelPrepPreference = ShipmentConstants.AmazonAPI.DefaultLabelPrepPreference,
            ShipFromAddress = CreateShipFromAddressString()
        });
    }

    /// <summary>
    /// Calculates box weights and dimensions
    /// </summary>
    public async Task<ProcessedBoxData> CalculateBoxMetricsAsync(ProcessedBoxData processedBox)
    {
        // Calculate total item weight (quantity * unit weight)
        processedBox.TotalItemWeightKg = processedBox.ItemWeightKg * processedBox.Quantity;

        // Add box weight (empty box weight in kg)
        processedBox.BoxWeightKg = ShipmentConstants.Conversions.DefaultBoxWeightGrams / 1000m;

        // Calculate total box weight (items + box)
        processedBox.TotalBoxWeightKg = processedBox.TotalItemWeightKg + processedBox.BoxWeightKg;

        _logger.LogInformation("Weight calculation for Box {BoxNumber}: {Quantity} × {UnitWeight} kg = {TotalItemWeight} kg + {BoxWeight} kg = {TotalBoxWeight} kg",
            processedBox.BoxNumber, processedBox.Quantity, processedBox.ItemWeightKg,
            processedBox.TotalItemWeightKg, processedBox.BoxWeightKg, processedBox.TotalBoxWeightKg);

        return await Task.FromResult(processedBox);
    }

    /// <summary>
    /// Validates complete shipment data before creation
    /// </summary>
    public async Task<(bool IsValid, List<string> Errors, List<string> Warnings)> ValidateShipmentDataAsync(
        CreateInboundShipmentRequest shipmentRequest)
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        // Use existing shipment service validation
        var (isValid, validationErrors) = await _shipmentService.ValidateShipmentRequestAsync(shipmentRequest);
        
        if (!isValid)
        {
            errors.AddRange(validationErrors);
        }

        // Additional validations specific to image processing
        if (shipmentRequest.Items.Count == 0)
        {
            errors.Add("No items found in shipment");
        }

        if (shipmentRequest.Boxes.Count == 0)
        {
            errors.Add("No boxes found in shipment");
        }

        // Check for weight limits
        foreach (var box in shipmentRequest.Boxes)
        {
            if (box.Weight.Value > ShipmentConstants.Validation.MaxWeight * ShipmentConstants.Conversions.KgToPounds)
            {
                warnings.Add($"Box {box.BoxId} exceeds recommended weight limit");
            }
        }

        return (errors.Count == 0, errors, warnings);
    }

    /// <summary>
    /// Gets processing statistics
    /// </summary>
    public async Task<ImageProcessingStatistics> GetProcessingStatisticsAsync()
    {
        return await Task.FromResult(_statistics);
    }

    #region Private Helper Methods

    private static BoxDimensions ParseDimensions(string dimensionsString)
    {
        var dimensions = new BoxDimensions { Unit = "cm" };

        if (string.IsNullOrWhiteSpace(dimensionsString))
        {
            // Default dimensions if not provided
            dimensions.Length = 30;
            dimensions.Width = 20;
            dimensions.Height = 15;
            return dimensions;
        }

        // Parse format like "58X38X26" or "58x38x26"
        var match = Regex.Match(dimensionsString.ToUpper(), @"(\d+(?:\.\d+)?)X(\d+(?:\.\d+)?)X(\d+(?:\.\d+)?)");
        
        if (match.Success &&
            decimal.TryParse(match.Groups[1].Value, out var length) &&
            decimal.TryParse(match.Groups[2].Value, out var width) &&
            decimal.TryParse(match.Groups[3].Value, out var height))
        {
            // Convert cm to inches for Amazon API
            dimensions.Length = Math.Round(length * ShipmentConstants.Conversions.CmToInches, 2);
            dimensions.Width = Math.Round(width * ShipmentConstants.Conversions.CmToInches, 2);
            dimensions.Height = Math.Round(height * ShipmentConstants.Conversions.CmToInches, 2);
            dimensions.Unit = "inches";
        }
        else
        {
            // Default dimensions if parsing fails
            dimensions.Length = 12; // inches
            dimensions.Width = 8;
            dimensions.Height = 6;
            dimensions.Unit = "inches";
        }

        return dimensions;
    }

    private static string CreateShipFromAddressString()
    {
        return $"{ShipmentConstants.ShipFromAddress.Name}, " +
               $"{ShipmentConstants.ShipFromAddress.AddressLine1}, " +
               $"{ShipmentConstants.ShipFromAddress.AddressLine2}, " +
               $"{ShipmentConstants.ShipFromAddress.City}, " +
               $"{ShipmentConstants.ShipFromAddress.StateOrProvince} " +
               $"{ShipmentConstants.ShipFromAddress.PostalCode}, " +
               $"{ShipmentConstants.ShipFromAddress.CountryCode}";
    }

    /// <summary>
    /// Creates consolidated item details (unique SKUs with total quantities)
    /// </summary>
    private static List<ConsolidatedItemDetails> CreateConsolidatedItemDetails(List<ProcessedBoxData> validBoxes)
    {
        var itemGroups = validBoxes
            .GroupBy(box => new { box.GeneratedSKU, box.MappedProductName, box.BarcodeType })
            .Select(group => new ConsolidatedItemDetails
            {
                Sku = group.Key.GeneratedSKU,
                ProductName = group.Key.MappedProductName,
                BarcodeType = group.Key.BarcodeType,
                TotalQuantity = group.Sum(box => box.Quantity),
                UnitWeightKg = group.First().ItemWeightKg,
                TotalWeightKg = group.Sum(box => box.TotalItemWeightKg),
                SourceBoxNumbers = group.Select(box => box.BoxNumber).OrderBy(x => x).ToList()
            })
            .OrderBy(item => item.Sku)
            .ToList();

        return itemGroups;
    }

    /// <summary>
    /// Creates consolidated box details with calculated weights
    /// </summary>
    private static List<ConsolidatedBoxDetails> CreateConsolidatedBoxDetails(List<ProcessedBoxData> validBoxes)
    {
        var boxDetails = validBoxes
            .GroupBy(box => box.BoxNumber)
            .Select(group => new ConsolidatedBoxDetails
            {
                BoxNumber = group.Key,
                BoxId = $"BOX-{group.Key:D3}", // Still maintaining BoxId even though we don't show it in UI
                TotalWeightKg = group.Sum(box => box.TotalItemWeightKg), // Sum of all items' weights in the box
                TotalWeightPounds = group.Sum(box => box.TotalItemWeightKg) * ShipmentConstants.Conversions.KgToPounds,
                Dimensions = group.First().ParsedDimensions, // Dimensions from first occurrence
                DimensionsString = group.First().DimensionsString, // Dimensions string from first occurrence
                Items = group.Select(box => new BoxItemSummary
                {
                    Sku = box.GeneratedSKU,
                    ProductName = box.MappedProductName,
                    Quantity = box.Quantity,
                    UnitWeightKg = box.ItemWeightKg,
                    TotalWeightKg = box.TotalItemWeightKg
                }).ToList()
            })
            .OrderBy(box => box.BoxNumber)
            .ToList();

        return boxDetails;
    }

    #endregion
}
