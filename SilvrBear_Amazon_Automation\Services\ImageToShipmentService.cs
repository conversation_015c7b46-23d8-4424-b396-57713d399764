using System.Diagnostics;
using System.Text.RegularExpressions;
using SilvrBear_Amazon_Automation.Constants;
using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Service for complete image to shipment processing workflow
/// </summary>
public class ImageToShipmentService : IImageToShipmentService
{
    private readonly IOpenAIImageService _openAIService;
    private readonly IProductMappingService _productMappingService;
    private readonly IInboundShipmentService _shipmentService;
    private readonly ILogger<ImageToShipmentService> _logger;
    private readonly ImageProcessingStatistics _statistics = new();

    public ImageToShipmentService(
        IOpenAIImageService openAIService,
        IProductMappingService productMappingService,
        IInboundShipmentService shipmentService,
        ILogger<ImageToShipmentService> logger)
    {
        _openAIService = openAIService;
        _productMappingService = productMappingService;
        _shipmentService = shipmentService;
        _logger = logger;
    }

    /// <summary>
    /// Processes an image and creates a complete inbound shipment
    /// </summary>
    public async Task<ImageToShipmentResult> ProcessImageAndCreateShipmentAsync(ImageToShipmentRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new ImageToShipmentResult();

        try
        {
            _logger.LogInformation("Starting complete image to shipment processing for: {FileName}", request.ImageFile.FileName);
            _statistics.TotalImagesProcessed++;

            // Step 1: Process image with OpenAI
            var availableProducts = await _productMappingService.GetAvailableProductNamesAsync();
            var imageResult = await _openAIService.ProcessImageAsync(request.ImageFile, availableProducts);
            result.ImageProcessingResult = imageResult;

            if (!imageResult.IsSuccess)
            {
                result.Errors.AddRange(imageResult.Errors);
                _statistics.FailedProcessing++;
                return result;
            }

            // Step 2: Map and enrich box data
            var enrichedBoxes = await MapAndEnrichBoxDataAsync(imageResult.ProcessedBoxes);
            result.TotalBoxesProcessed = enrichedBoxes.Count;
            result.ValidBoxes = enrichedBoxes.Count(b => b.IsValid);
            result.InvalidBoxes = enrichedBoxes.Count(b => !b.IsValid);

            if (result.ValidBoxes == 0)
            {
                result.Errors.Add("No valid boxes found after processing and mapping");
                _statistics.FailedProcessing++;
                return result;
            }

            // Step 3: Convert to shipment request
            var shipmentName = request.ShipmentName ?? $"IMG-Shipment-{DateTime.UtcNow:yyyyMMdd-HHmmss}";
            var fulfillmentCenterId = request.FulfillmentCenterId ?? ShipmentConstants.FulfillmentCenters.DefaultFulfillmentCenterId;

            var shipmentRequest = await ConvertToShipmentRequestAsync(enrichedBoxes.Where(b => b.IsValid).ToList(), shipmentName, fulfillmentCenterId);

            // Step 4: Validate shipment data
            var (isValid, validationErrors, validationWarnings) = await ValidateShipmentDataAsync(shipmentRequest);
            result.Warnings.AddRange(validationWarnings);

            if (!isValid)
            {
                result.Errors.AddRange(validationErrors);
                _statistics.FailedProcessing++;
                return result;
            }

            // Step 5: Create shipment if requested
            if (request.AutoCreateShipment && !request.ValidateOnly)
            {
                var shipmentResponse = await _shipmentService.CreateCompleteInboundShipmentAsync(shipmentRequest);
                result.ShipmentResponse = shipmentResponse;

                if (shipmentResponse.Errors.Any())
                {
                    result.Errors.AddRange(shipmentResponse.Errors);
                    _statistics.FailedProcessing++;
                    return result;
                }

                result.ShipmentId = shipmentResponse.ShipmentId;
                _statistics.TotalShipmentsCreated++;
                _logger.LogInformation("Successfully created shipment: {ShipmentId}", result.ShipmentId);
            }

            result.IsSuccess = true;
            _statistics.SuccessfulProcessing++;
            _statistics.TotalBoxesExtracted += result.ValidBoxes;

            // Update product statistics
            foreach (var box in enrichedBoxes.Where(b => b.IsValid))
            {
                var productKey = box.MappedProductName;
                _statistics.ProductCounts[productKey] = _statistics.ProductCounts.GetValueOrDefault(productKey, 0) + 1;
            }

            _logger.LogInformation("Image to shipment processing completed successfully. Boxes: {ValidBoxes}/{TotalBoxes}", 
                result.ValidBoxes, result.TotalBoxesProcessed);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in image to shipment processing: {Error}", ex.Message);
            result.Errors.Add($"Processing error: {ex.Message}");
            _statistics.FailedProcessing++;

            var errorKey = ex.GetType().Name;
            _statistics.ErrorCounts[errorKey] = _statistics.ErrorCounts.GetValueOrDefault(errorKey, 0) + 1;
        }
        finally
        {
            stopwatch.Stop();
            result.TotalProcessingTime = stopwatch.Elapsed;
            _statistics.LastProcessedAt = DateTime.UtcNow;
            
            // Update average processing time
            var totalTime = _statistics.AverageProcessingTime.TotalMilliseconds * (_statistics.TotalImagesProcessed - 1) + 
                           stopwatch.Elapsed.TotalMilliseconds;
            _statistics.AverageProcessingTime = TimeSpan.FromMilliseconds(totalTime / _statistics.TotalImagesProcessed);
        }

        return result;
    }

    /// <summary>
    /// Processes an image and validates data without creating shipment
    /// </summary>
    public async Task<ImageToShipmentResult> ValidateImageDataAsync(
        IFormFile imageFile, 
        string? shipmentName = null, 
        string? fulfillmentCenterId = null)
    {
        var request = new ImageToShipmentRequest
        {
            ImageFile = imageFile,
            ShipmentName = shipmentName,
            FulfillmentCenterId = fulfillmentCenterId,
            AutoCreateShipment = false,
            ValidateOnly = true
        };

        return await ProcessImageAndCreateShipmentAsync(request);
    }

    /// <summary>
    /// Processes extracted box data and maps to products
    /// </summary>
    public async Task<List<ProcessedBoxData>> MapAndEnrichBoxDataAsync(List<ProcessedBoxData> processedBoxes)
    {
        var enrichedBoxes = new List<ProcessedBoxData>();

        foreach (var box in processedBoxes)
        {
            try
            {
                // Find best matching product
                var mappedProduct = await _productMappingService.FindBestMatchAsync(box.OriginalProductName, box.BarcodeType);
                
                if (mappedProduct != null)
                {
                    box.MappedProductName = mappedProduct.ProductName;
                    box.GeneratedSKU = await _productMappingService.GenerateSKUAsync(mappedProduct.ProductName, box.BarcodeType, box.BoxNumber);
                    box.ItemWeightKg = mappedProduct.WeightKg;
                }
                else
                {
                    box.MappedProductName = box.OriginalProductName;
                    box.GeneratedSKU = await _productMappingService.GenerateSKUAsync(box.OriginalProductName, box.BarcodeType, box.BoxNumber);
                    box.ItemWeightKg = await _productMappingService.GetProductWeightAsync(box.OriginalProductName, box.BarcodeType);
                    box.ValidationWarnings.Add($"Product '{box.OriginalProductName}' not found in mapping. Using default values.");
                }

                // Calculate metrics
                var calculatedBox = await CalculateBoxMetricsAsync(box);
                box.TotalItemWeightKg = calculatedBox.TotalItemWeightKg;
                box.BoxWeightKg = calculatedBox.BoxWeightKg;
                box.TotalBoxWeightKg = calculatedBox.TotalBoxWeightKg;
                
                // Parse dimensions
                box.ParsedDimensions = ParseDimensions(box.DimensionsString);

                enrichedBoxes.Add(box);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error enriching box {BoxNumber}: {Error}", box.BoxNumber, ex.Message);
                box.ValidationErrors.Add($"Error processing box: {ex.Message}");
                box.IsValid = false;
                enrichedBoxes.Add(box);
            }
        }

        return enrichedBoxes;
    }

    /// <summary>
    /// Converts processed box data to shipment request
    /// </summary>
    public async Task<CreateInboundShipmentRequest> ConvertToShipmentRequestAsync(
        List<ProcessedBoxData> processedBoxes,
        string shipmentName,
        string fulfillmentCenterId)
    {
        var shipmentItems = new List<ShipmentItem>();
        var shipmentBoxes = new List<ShipmentBox>();

        foreach (var box in processedBoxes.Where(b => b.IsValid))
        {
            // Create shipment item
            var shipmentItem = new ShipmentItem
            {
                Sku = box.GeneratedSKU,
                Quantity = box.Quantity,
                ProductName = box.MappedProductName,
                Condition = "NewItem",
                UnitCost = 0 // Will be set later if needed
            };
            shipmentItems.Add(shipmentItem);

            // Create shipment box
            var shipmentBox = new ShipmentBox
            {
                BoxId = $"BOX-{box.BoxNumber:D3}",
                Dimensions = new BoxDimensions
                {
                    Length = box.ParsedDimensions.Length,
                    Width = box.ParsedDimensions.Width,
                    Height = box.ParsedDimensions.Height,
                    Unit = "inches" // Convert to inches for Amazon API
                },
                Weight = new BoxWeight
                {
                    Value = box.TotalBoxWeightKg * ShipmentConstants.Conversions.KgToPounds, // Convert to pounds
                    Unit = "pounds"
                },
                Contents = new List<BoxContent>
                {
                    new()
                    {
                        Sku = box.GeneratedSKU,
                        Quantity = box.Quantity
                    }
                }
            };
            shipmentBoxes.Add(shipmentBox);
        }

        return new CreateInboundShipmentRequest
        {
            ShipmentName = shipmentName,
            DestinationFulfillmentCenterId = fulfillmentCenterId,
            Items = shipmentItems,
            Boxes = shipmentBoxes,
            LabelPrepPreference = ShipmentConstants.AmazonAPI.DefaultLabelPrepPreference,
            ShipFromAddress = CreateShipFromAddress()
        };
    }

    /// <summary>
    /// Calculates box weights and dimensions
    /// </summary>
    public async Task<ProcessedBoxData> CalculateBoxMetricsAsync(ProcessedBoxData processedBox)
    {
        // Calculate total item weight
        processedBox.TotalItemWeightKg = processedBox.ItemWeightKg * processedBox.Quantity;
        
        // Add box weight (empty box weight)
        processedBox.BoxWeightKg = ShipmentConstants.Conversions.DefaultBoxWeightGrams / 1000m;
        
        // Calculate total box weight
        processedBox.TotalBoxWeightKg = processedBox.TotalItemWeightKg + processedBox.BoxWeightKg;

        return await Task.FromResult(processedBox);
    }

    /// <summary>
    /// Validates complete shipment data before creation
    /// </summary>
    public async Task<(bool IsValid, List<string> Errors, List<string> Warnings)> ValidateShipmentDataAsync(
        CreateInboundShipmentRequest shipmentRequest)
    {
        var errors = new List<string>();
        var warnings = new List<string>();

        // Use existing shipment service validation
        var (isValid, validationErrors) = await _shipmentService.ValidateShipmentRequestAsync(shipmentRequest);
        
        if (!isValid)
        {
            errors.AddRange(validationErrors);
        }

        // Additional validations specific to image processing
        if (shipmentRequest.Items.Count == 0)
        {
            errors.Add("No items found in shipment");
        }

        if (shipmentRequest.Boxes.Count == 0)
        {
            errors.Add("No boxes found in shipment");
        }

        // Check for weight limits
        foreach (var box in shipmentRequest.Boxes)
        {
            if (box.Weight.Value > ShipmentConstants.Validation.MaxWeight * ShipmentConstants.Conversions.KgToPounds)
            {
                warnings.Add($"Box {box.BoxId} exceeds recommended weight limit");
            }
        }

        return (errors.Count == 0, errors, warnings);
    }

    /// <summary>
    /// Gets processing statistics
    /// </summary>
    public async Task<ImageProcessingStatistics> GetProcessingStatisticsAsync()
    {
        return await Task.FromResult(_statistics);
    }

    #region Private Helper Methods

    private static BoxDimensions ParseDimensions(string dimensionsString)
    {
        var dimensions = new BoxDimensions { Unit = "cm" };

        if (string.IsNullOrWhiteSpace(dimensionsString))
        {
            // Default dimensions if not provided
            dimensions.Length = 30;
            dimensions.Width = 20;
            dimensions.Height = 15;
            return dimensions;
        }

        // Parse format like "58X38X26" or "58x38x26"
        var match = Regex.Match(dimensionsString.ToUpper(), @"(\d+(?:\.\d+)?)X(\d+(?:\.\d+)?)X(\d+(?:\.\d+)?)");
        
        if (match.Success &&
            decimal.TryParse(match.Groups[1].Value, out var length) &&
            decimal.TryParse(match.Groups[2].Value, out var width) &&
            decimal.TryParse(match.Groups[3].Value, out var height))
        {
            // Convert cm to inches for Amazon API
            dimensions.Length = Math.Round(length * ShipmentConstants.Conversions.CmToInches, 2);
            dimensions.Width = Math.Round(width * ShipmentConstants.Conversions.CmToInches, 2);
            dimensions.Height = Math.Round(height * ShipmentConstants.Conversions.CmToInches, 2);
            dimensions.Unit = "inches";
        }
        else
        {
            // Default dimensions if parsing fails
            dimensions.Length = 12; // inches
            dimensions.Width = 8;
            dimensions.Height = 6;
            dimensions.Unit = "inches";
        }

        return dimensions;
    }

    private static string CreateShipFromAddress()
    {
        return $"{ShipmentConstants.ShipFromAddress.Name}, " +
               $"{ShipmentConstants.ShipFromAddress.AddressLine1}, " +
               $"{ShipmentConstants.ShipFromAddress.AddressLine2}, " +
               $"{ShipmentConstants.ShipFromAddress.City}, " +
               $"{ShipmentConstants.ShipFromAddress.StateOrProvince} " +
               $"{ShipmentConstants.ShipFromAddress.PostalCode}, " +
               $"{ShipmentConstants.ShipFromAddress.CountryCode}";
    }

    #endregion
}
