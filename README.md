# SilvrBear Amazon FBA Automation

A .NET 8 Blazor Server application for automating Amazon FBA inbound shipment creation using the **Amazon Seller API v2024-03-20** (latest version).

## 🚀 Latest Updates

- **✅ Migrated to Amazon Fulfillment Inbound API v2024-03-20** (December 2024)
- **✅ Enhanced Image Processing** with improved product matching and SKU validation
- **✅ India Market Support** with proper marketplace configuration and fulfillment centers
- **✅ Advanced Error Handling** with user-friendly error messages for the new API

## Features

- **Latest API Version**: Uses Amazon Fulfillment Inbound API v2024-03-20 (not deprecated v0)
- **Multi-step Workflow**: Implements the new API workflow (create plan → generate packing options → confirm)
- **Image Processing**: Extract shipment data from handwritten tables using OpenAI Vision API
- **Enhanced Product Matching**: Fuzzy matching for product names with mapping file integration
- **India Market Ready**: Configured for Amazon India marketplace with proper fulfillment centers
- **SKU Management**: Add multiple SKUs with quantities and product information
- **Box Specification**: Define box dimensions and weights for accurate shipping
- **Real-time Validation**: Form validation and error handling with enhanced error messages
- **Responsive UI**: Modern Bootstrap-based interface

## Architecture

### Models
- `AmazonCredentials`: Configuration for Amazon API credentials
- `InboundShipmentModels`: Data models for shipments, items, and boxes
- `ApiResponseModels`: Models for Amazon API responses

### Services
- `AuthenticationService`: Handles Amazon LWA token authentication
- `AmazonApiClient`: HTTP client wrapper for Amazon API calls
- `AmazonSellerApiService`: Low-level Amazon Seller API operations
- `InboundShipmentService`: High-level business logic for shipment management

### UI Components
- `InboundShipment.razor`: Main page for creating shipments
- Responsive forms for item and box management
- Real-time status updates and error handling

## Configuration

Update the `appsettings.json` file with your Amazon Seller API sandbox credentials:

```json
{
  "AmazonCredentials": {
    "ClientId": "YOUR_SANDBOX_CLIENT_ID",
    "ClientSecret": "YOUR_SANDBOX_CLIENT_SECRET",
    "RefreshToken": "YOUR_SANDBOX_REFRESH_TOKEN",
    "MarketplaceId": "ATVPDKIKX0DER",
    "SellerId": "YOUR_SANDBOX_SELLER_ID",
    "BaseUrl": "https://sellingpartnerapi-na.amazon.com",
    "LwaTokenUrl": "https://api.amazon.com/auth/o2/token",
    "AwsRegion": "us-east-1",
    "IsSandbox": true
  }
}
```

## Getting Started

1. **Prerequisites**
   - .NET 8 SDK
   - Amazon Seller Central account with API access
   - Sandbox credentials from Amazon Developer Console

2. **Setup**
   ```bash
   git clone <repository-url>
   cd SilvrBear_Amazon_Automation
   dotnet restore
   ```

3. **Configure Credentials**
   - Update `appsettings.json` with your sandbox credentials
   - Ensure `IsSandbox` is set to `true` for testing

4. **Run the Application**
   ```bash
   cd SilvrBear_Amazon_Automation
   dotnet run
   ```

5. **Access the Application**
   - Navigate to `https://localhost:7173` or `http://localhost:5254`
   - Click on "Inbound Shipment" in the navigation menu

## Usage

### Creating an Inbound Shipment

1. **Basic Information**
   - Enter a descriptive shipment name
   - Select the destination fulfillment center

2. **Add Items**
   - Click "Add Item" to add products
   - Enter SKU, product name, quantity, and ASIN
   - Add multiple items as needed

3. **Specify Boxes**
   - Click "Add Box" to define packaging
   - Enter box ID, dimensions (length, width, height in inches)
   - Specify weight in pounds
   - Add multiple boxes as needed

4. **Create Shipment**
   - Click "Create Inbound Shipment"
   - Monitor the status and any error messages
   - View the created shipment details

## API Integration

The application uses the **Amazon Fulfillment Inbound API v2024-03-20** (latest version) with the following endpoints:

### New v2024-03-20 API Endpoints
- **Create Inbound Plan**: `/inbound/fba/2024-03-20/inboundPlans`
- **Generate Packing Options**: `/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions`
- **List Packing Options**: `/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions`
- **Confirm Packing Option**: `/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingOptions/{packingOptionId}/confirmation`
- **Confirm Placement Option**: `/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/placementOptions/{packingOptionId}/confirmation`
- **Set Packing Information**: `/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/packingInformation`
- **Generate Transportation Options**: `/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions`
- **Confirm Transportation Options**: `/inbound/fba/2024-03-20/inboundPlans/{inboundPlanId}/transportationOptions/{transportationOptionId}/confirmation`

### Preserved v0 API Endpoints (Still Available)
- **Get Shipment Details**: `/fba/inbound/v0/shipments/{shipmentId}`
- **List Shipments**: `/fba/inbound/v0/shipments`
- **Get Labels**: `/fba/inbound/v0/shipments/{shipmentId}/labels`
- **Get Bill of Lading**: `/fba/inbound/v0/shipments/{shipmentId}/billOfLading`

### Migration Benefits
- **Future-proof**: Uses the latest API version that won't be deprecated
- **Enhanced workflow**: Multi-step process for better shipment planning
- **Better error handling**: Improved error messages and validation
- **More flexibility**: Advanced packing and transportation options
- **Get Shipment**: `/fba/inbound/v0/shipments/{shipmentId}`
## 🔄 API Migration (v0 → v2024-03-20)

This application has been **fully migrated** from the deprecated Amazon Fulfillment Inbound API v0 to the latest v2024-03-20 version.

### What Changed
- **Multi-step workflow**: The new API requires multiple API calls instead of single operations
- **Enhanced data structures**: New request/response models with more detailed information
- **Better error handling**: More specific error codes and messages
- **Future-proof**: No more deprecation warnings

### Migration Impact
- **Backward compatibility**: Preserved operations (getLabels, getShipments) still work
- **Enhanced features**: Better packing options and transportation management
- **Improved reliability**: More robust error handling and validation

### For Developers
If you're maintaining this codebase:
1. All new shipment creation uses v2024-03-20 endpoints
2. Legacy operations use preserved v0 endpoints where available
3. Error handling includes both API versions
4. Models support both old and new response formats

## Security

- All credentials are stored in configuration files
- Uses Amazon's LWA (Login with Amazon) for authentication
- Implements proper token refresh mechanisms
- Sandbox environment prevents accidental production operations

## Development

### Project Structure
```
SilvrBear_Amazon_Automation/
├── Components/
│   ├── Layout/
│   └── Pages/
│       └── InboundShipment.razor
├── Models/
│   ├── AmazonCredentials.cs
│   ├── InboundShipmentModels.cs
│   └── ApiResponseModels.cs
├── Services/
│   ├── AuthenticationService.cs
│   ├── AmazonApiClient.cs
│   ├── AmazonSellerApiService.cs
│   └── InboundShipmentService.cs
├── Program.cs
└── appsettings.json
```

### Building
```bash
dotnet build
```

### Running Tests
```bash
dotnet test
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify sandbox credentials are correct
   - Check that refresh token is valid
   - Ensure marketplace ID matches your region (India: A21TJRUUN4KGV)

2. **v2024-03-20 API Errors**
   - **"No packing options available"**: Check item quantities and dimensions
   - **"Inbound plan not found"**: Plan may have expired, create a new one
   - **"Invalid argument"**: Verify all required fields in the new API format
   - **Rate limiting**: The new API has different rate limits, wait between calls

3. **Legacy v0 API Errors**
   - Some operations still use v0 endpoints (getLabels, getShipments)
   - These may show deprecation warnings but still function

4. **Validation Errors**
   - Ensure all required fields are filled
   - Check SKU format and existence in mapping files
   - Verify box dimensions and weights are positive
   - India-specific: Use proper state codes (MH for Maharashtra)

5. **Image Processing Issues**
   - Ensure OpenAI API key is configured
   - Check image format (JPG, PNG supported)
   - Verify mapping Excel file is accessible

## License

This project is for educational and development purposes. Ensure compliance with Amazon's API terms of service.

## Support

For issues related to Amazon Seller API, consult the [Amazon Seller Central documentation](https://developer-docs.amazon.com/sp-api/).

## Disclaimer

This application is configured for Amazon's sandbox environment. No real shipments will be created or processed. Always test thoroughly before using with production credentials.