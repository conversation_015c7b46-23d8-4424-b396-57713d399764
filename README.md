# SilvrBear Amazon FBA Automation

A .NET 8 Blazor Server application for automating Amazon FBA inbound shipment creation using the Amazon Seller API.

## Features

- **Sandbox Environment**: Configured for Amazon's sandbox environment for safe testing
- **Inbound Shipment Creation**: Complete automation of FBA inbound shipment creation
- **SKU Management**: Add multiple SKUs with quantities and product information
- **Box Specification**: Define box dimensions and weights for accurate shipping
- **Fulfillment Center Selection**: Choose from available Amazon fulfillment centers
- **Real-time Validation**: Form validation and error handling
- **Responsive UI**: Modern Bootstrap-based interface

## Architecture

### Models
- `AmazonCredentials`: Configuration for Amazon API credentials
- `InboundShipmentModels`: Data models for shipments, items, and boxes
- `ApiResponseModels`: Models for Amazon API responses

### Services
- `AuthenticationService`: Handles Amazon LWA token authentication
- `AmazonApiClient`: HTTP client wrapper for Amazon API calls
- `AmazonSellerApiService`: Low-level Amazon Seller API operations
- `InboundShipmentService`: High-level business logic for shipment management

### UI Components
- `InboundShipment.razor`: Main page for creating shipments
- Responsive forms for item and box management
- Real-time status updates and error handling

## Configuration

Update the `appsettings.json` file with your Amazon Seller API sandbox credentials:

```json
{
  "AmazonCredentials": {
    "ClientId": "YOUR_SANDBOX_CLIENT_ID",
    "ClientSecret": "YOUR_SANDBOX_CLIENT_SECRET",
    "RefreshToken": "YOUR_SANDBOX_REFRESH_TOKEN",
    "MarketplaceId": "ATVPDKIKX0DER",
    "SellerId": "YOUR_SANDBOX_SELLER_ID",
    "BaseUrl": "https://sellingpartnerapi-na.amazon.com",
    "LwaTokenUrl": "https://api.amazon.com/auth/o2/token",
    "AwsRegion": "us-east-1",
    "IsSandbox": true
  }
}
```

## Getting Started

1. **Prerequisites**
   - .NET 8 SDK
   - Amazon Seller Central account with API access
   - Sandbox credentials from Amazon Developer Console

2. **Setup**
   ```bash
   git clone <repository-url>
   cd SilvrBear_Amazon_Automation
   dotnet restore
   ```

3. **Configure Credentials**
   - Update `appsettings.json` with your sandbox credentials
   - Ensure `IsSandbox` is set to `true` for testing

4. **Run the Application**
   ```bash
   cd SilvrBear_Amazon_Automation
   dotnet run
   ```

5. **Access the Application**
   - Navigate to `https://localhost:7173` or `http://localhost:5254`
   - Click on "Inbound Shipment" in the navigation menu

## Usage

### Creating an Inbound Shipment

1. **Basic Information**
   - Enter a descriptive shipment name
   - Select the destination fulfillment center

2. **Add Items**
   - Click "Add Item" to add products
   - Enter SKU, product name, quantity, and ASIN
   - Add multiple items as needed

3. **Specify Boxes**
   - Click "Add Box" to define packaging
   - Enter box ID, dimensions (length, width, height in inches)
   - Specify weight in pounds
   - Add multiple boxes as needed

4. **Create Shipment**
   - Click "Create Inbound Shipment"
   - Monitor the status and any error messages
   - View the created shipment details

## API Integration

The application integrates with the following Amazon Seller API endpoints:

- **Inbound Shipment Plans**: `/fba/inbound/v0/plans`
- **Create Shipment**: `/fba/inbound/v0/shipments/{shipmentId}`
- **Update Shipment**: `/fba/inbound/v0/shipments/{shipmentId}`
- **Get Shipment**: `/fba/inbound/v0/shipments/{shipmentId}`
- **List Shipments**: `/fba/inbound/v0/shipments`
- **Transport Management**: Various transport-related endpoints
- **Labels**: `/fba/inbound/v0/shipments/{shipmentId}/labels`

## Security

- All credentials are stored in configuration files
- Uses Amazon's LWA (Login with Amazon) for authentication
- Implements proper token refresh mechanisms
- Sandbox environment prevents accidental production operations

## Development

### Project Structure
```
SilvrBear_Amazon_Automation/
├── Components/
│   ├── Layout/
│   └── Pages/
│       └── InboundShipment.razor
├── Models/
│   ├── AmazonCredentials.cs
│   ├── InboundShipmentModels.cs
│   └── ApiResponseModels.cs
├── Services/
│   ├── AuthenticationService.cs
│   ├── AmazonApiClient.cs
│   ├── AmazonSellerApiService.cs
│   └── InboundShipmentService.cs
├── Program.cs
└── appsettings.json
```

### Building
```bash
dotnet build
```

### Running Tests
```bash
dotnet test
```

## Troubleshooting

### Common Issues

1. **Authentication Errors**
   - Verify sandbox credentials are correct
   - Check that refresh token is valid
   - Ensure marketplace ID matches your region

2. **API Errors**
   - Confirm you're using sandbox endpoints
   - Check rate limiting (Amazon has API call limits)
   - Verify your seller account has FBA permissions

3. **Validation Errors**
   - Ensure all required fields are filled
   - Check SKU format and existence
   - Verify box dimensions and weights are positive

## License

This project is for educational and development purposes. Ensure compliance with Amazon's API terms of service.

## Support

For issues related to Amazon Seller API, consult the [Amazon Seller Central documentation](https://developer-docs.amazon.com/sp-api/).

## Disclaimer

This application is configured for Amazon's sandbox environment. No real shipments will be created or processed. Always test thoroughly before using with production credentials.