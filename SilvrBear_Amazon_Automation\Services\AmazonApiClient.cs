using Microsoft.Extensions.Options;
using SilvrBear_Amazon_Automation.Models;
using System.Text;
using System.Text.Json;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// HTTP client wrapper for Amazon Seller API calls
/// </summary>
public class AmazonApiClient
{
    private readonly HttpClient _httpClient;
    private readonly AuthenticationService _authService;
    private readonly AmazonCredentials _credentials;
    private readonly ILogger<AmazonApiClient> _logger;
    private readonly JsonSerializerOptions _jsonOptions;

    public AmazonApiClient(
        HttpClient httpClient,
        AuthenticationService authService,
        IOptions<AmazonCredentials> credentials,
        ILogger<AmazonApiClient> logger)
    {
        _httpClient = httpClient;
        _authService = authService;
        _credentials = credentials.Value;
        _logger = logger;
        
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        };

        // Configure HttpClient base address
        _httpClient.BaseAddress = new Uri(_credentials.BaseUrl);
    }

    /// <summary>
    /// Makes a GET request to the Amazon API
    /// </summary>
    /// <typeparam name="T">Response type</typeparam>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="queryParameters">Query parameters</param>
    /// <returns>API response</returns>
    public async Task<AmazonApiResponse<T>> GetAsync<T>(string endpoint, Dictionary<string, string>? queryParameters = null)
    {
        try
        {
            var url = BuildUrl(endpoint, queryParameters);
            var headers = await _authService.GetAuthorizationHeadersAsync();

            var request = new HttpRequestMessage(HttpMethod.Get, url);
            AddHeaders(request, headers);

            _logger.LogInformation("Making GET request to: {Url}", url);

            var response = await _httpClient.SendAsync(request);
            return await ProcessResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error making GET request to {Endpoint}", endpoint);
            return CreateErrorResponse<T>(ex.Message);
        }
    }

    /// <summary>
    /// Makes a POST request to the Amazon API
    /// </summary>
    /// <typeparam name="T">Response type</typeparam>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="payload">Request payload</param>
    /// <returns>API response</returns>
    public async Task<AmazonApiResponse<T>> PostAsync<T>(string endpoint, object? payload = null)
    {
        try
        {
            var headers = await _authService.GetAuthorizationHeadersAsync();
            var request = new HttpRequestMessage(HttpMethod.Post, endpoint);
            
            if (payload != null)
            {
                var json = JsonSerializer.Serialize(payload, _jsonOptions);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                _logger.LogDebug("POST payload: {Payload}", json);
            }

            AddHeaders(request, headers);

            _logger.LogInformation("Making POST request to: {Endpoint}", endpoint);

            var response = await _httpClient.SendAsync(request);
            return await ProcessResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error making POST request to {Endpoint}", endpoint);
            return CreateErrorResponse<T>(ex.Message);
        }
    }

    /// <summary>
    /// Makes a PUT request to the Amazon API
    /// </summary>
    /// <typeparam name="T">Response type</typeparam>
    /// <param name="endpoint">API endpoint</param>
    /// <param name="payload">Request payload</param>
    /// <returns>API response</returns>
    public async Task<AmazonApiResponse<T>> PutAsync<T>(string endpoint, object? payload = null)
    {
        try
        {
            var headers = await _authService.GetAuthorizationHeadersAsync();
            var request = new HttpRequestMessage(HttpMethod.Put, endpoint);
            
            if (payload != null)
            {
                var json = JsonSerializer.Serialize(payload, _jsonOptions);
                request.Content = new StringContent(json, Encoding.UTF8, "application/json");
                _logger.LogDebug("PUT payload: {Payload}", json);
            }

            AddHeaders(request, headers);

            _logger.LogInformation("Making PUT request to: {Endpoint}", endpoint);

            var response = await _httpClient.SendAsync(request);
            return await ProcessResponse<T>(response);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error making PUT request to {Endpoint}", endpoint);
            return CreateErrorResponse<T>(ex.Message);
        }
    }

    /// <summary>
    /// Builds URL with query parameters
    /// </summary>
    private string BuildUrl(string endpoint, Dictionary<string, string>? queryParameters)
    {
        if (queryParameters == null || !queryParameters.Any())
            return endpoint;

        var queryString = string.Join("&", queryParameters.Select(kvp => $"{kvp.Key}={Uri.EscapeDataString(kvp.Value)}"));
        return $"{endpoint}?{queryString}";
    }

    /// <summary>
    /// Adds headers to the request
    /// </summary>
    private void AddHeaders(HttpRequestMessage request, Dictionary<string, string> headers)
    {
        foreach (var header in headers)
        {
            if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase))
                continue; // Content-Type is handled by StringContent

            request.Headers.TryAddWithoutValidation(header.Key, header.Value);
        }
    }

    /// <summary>
    /// Processes the HTTP response
    /// </summary>
    private async Task<AmazonApiResponse<T>> ProcessResponse<T>(HttpResponseMessage response)
    {
        var content = await response.Content.ReadAsStringAsync();
        
        _logger.LogDebug("Response Status: {StatusCode}, Content: {Content}", response.StatusCode, content);

        if (!response.IsSuccessStatusCode)
        {
            _logger.LogWarning("API request failed with status {StatusCode}: {Content}", response.StatusCode, content);

            // Try to parse error response
            try
            {
                var errorResponse = JsonSerializer.Deserialize<AmazonApiResponse<T>>(content, _jsonOptions);
                if (errorResponse != null)
                {
                    // Enhance error messages for v2024-03-20 API specific errors
                    foreach (var error in errorResponse.Errors)
                    {
                        error.Message = EnhanceErrorMessage(error.Code, error.Message, response.StatusCode);
                    }
                    return errorResponse;
                }
            }
            catch (JsonException)
            {
                // If we can't parse the error response, create a generic error
            }

            return CreateErrorResponse<T>(EnhanceErrorMessage("HTTP_ERROR", content, response.StatusCode));
        }

        try
        {
            _logger.LogInformation("Attempting to deserialize response: {Content}", content);
            var apiResponse = JsonSerializer.Deserialize<AmazonApiResponse<T>>(content, _jsonOptions);
            return apiResponse ?? CreateErrorResponse<T>("Failed to deserialize response");
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to deserialize response: {Content}", content);
            return CreateErrorResponse<T>($"Deserialization error: {ex.Message}");
        }
    }

    /// <summary>
    /// Creates an error response
    /// </summary>
    private AmazonApiResponse<T> CreateErrorResponse<T>(string message)
    {
        return new AmazonApiResponse<T>
        {
            Errors = new List<ApiError>
            {
                new ApiError
                {
                    Code = "CLIENT_ERROR",
                    Message = message
                }
            }
        };
    }

    /// <summary>
    /// Enhances error messages for better user understanding, especially for v2024-03-20 API
    /// </summary>
    private string EnhanceErrorMessage(string errorCode, string originalMessage, System.Net.HttpStatusCode statusCode)
    {
        return errorCode.ToUpper() switch
        {
            "INVALID_ARGUMENT" => "Invalid request parameters. Please check your input data and try again.",
            "RESOURCE_NOT_FOUND" => "The requested resource was not found. Please verify the ID and try again.",
            "PERMISSION_DENIED" => "Access denied. Please check your API permissions and credentials.",
            "QUOTA_EXCEEDED" => "API rate limit exceeded. Please wait before making more requests.",
            "INTERNAL_SERVER_ERROR" or "INTERNALSERVERERROR" => "Amazon's servers are experiencing issues. Please try again later.",
            "INVALID_ACCESS_TOKEN" => "Your access token is invalid or expired. Please refresh your credentials.",
            "UNAUTHORIZED" => "Authentication failed. Please check your API credentials.",
            "DEPRECATED_API" => "This API version is deprecated. Please migrate to the latest version.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.BadRequest =>
                "Bad request. Please check your request format and parameters.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.Unauthorized =>
                "Authentication failed. Please check your API credentials.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.Forbidden =>
                "Access forbidden. You don't have permission to access this resource.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.NotFound =>
                "Resource not found. Please verify the endpoint and resource ID.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.TooManyRequests =>
                "Too many requests. Please wait before making more API calls.",
            "HTTP_ERROR" when statusCode == System.Net.HttpStatusCode.InternalServerError =>
                "Amazon's servers are experiencing issues. Please try again later.",
            _ => string.IsNullOrEmpty(originalMessage) ? $"API Error ({errorCode})" : originalMessage
        };
    }
}
