using System.Text.Json.Serialization;

namespace SilvrBear_Amazon_Automation.Models;

/// <summary>
/// Base response model for Amazon API calls
/// </summary>
public class AmazonApiResponse<T>
{
    [JsonPropertyName("payload")]
    public T? Payload { get; set; }

    [JsonPropertyName("errors")]
    public List<ApiError> Errors { get; set; } = new();

    public bool IsSuccess => !Errors.Any();
}

/// <summary>
/// API error model
/// </summary>
public class ApiError
{
    [JsonPropertyName("code")]
    public string Code { get; set; } = string.Empty;

    [JsonPropertyName("message")]
    public string Message { get; set; } = string.Empty;

    [JsonPropertyName("details")]
    public string? Details { get; set; }
}

/// <summary>
/// LWA (Login with Amazon) token response
/// </summary>
public class LwaTokenResponse
{
    [JsonPropertyName("access_token")]
    public string AccessToken { get; set; } = string.Empty;

    [JsonPropertyName("token_type")]
    public string TokenType { get; set; } = string.Empty;

    [JsonPropertyName("expires_in")]
    public int ExpiresIn { get; set; }

    [JsonPropertyName("refresh_token")]
    public string? RefreshToken { get; set; }
}

/// <summary>
/// Inbound shipment plan response (v2024-03-20)
/// </summary>
public class InboundShipmentPlanResponse
{
    [JsonPropertyName("inboundPlanId")]
    public string InboundPlanId { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("sourceAddress")]
    public Address? SourceAddress { get; set; }

    [JsonPropertyName("destinationMarketplaces")]
    public List<string> DestinationMarketplaces { get; set; } = new();

    [JsonPropertyName("status")]
    public string Status { get; set; } = string.Empty;

    [JsonPropertyName("operationId")]
    public string OperationId { get; set; } = string.Empty;
}

/// <summary>
/// Legacy inbound shipment plan (kept for backward compatibility)
/// </summary>
public class InboundShipmentPlan
{
    [JsonPropertyName("ShipmentId")]
    public string ShipmentId { get; set; } = string.Empty;

    [JsonPropertyName("DestinationFulfillmentCenterId")]
    public string DestinationFulfillmentCenterId { get; set; } = string.Empty;

    [JsonPropertyName("ShipToAddress")]
    public Address? ShipToAddress { get; set; }

    [JsonPropertyName("LabelPrepType")]
    public string LabelPrepType { get; set; } = string.Empty;

    [JsonPropertyName("Items")]
    public List<InboundShipmentPlanItem> Items { get; set; } = new();

    [JsonPropertyName("EstimatedBoxContentsFee")]
    public Money? EstimatedBoxContentsFee { get; set; }
}

/// <summary>
/// Inbound shipment plan item
/// </summary>
public class InboundShipmentPlanItem
{
    [JsonPropertyName("SellerSKU")]
    public string SellerSku { get; set; } = string.Empty;

    [JsonPropertyName("FulfillmentNetworkSKU")]
    public string FulfillmentNetworkSku { get; set; } = string.Empty;

    [JsonPropertyName("Quantity")]
    public int Quantity { get; set; }

    [JsonPropertyName("PrepDetailsList")]
    public List<PrepDetails> PrepDetailsList { get; set; } = new();
}

/// <summary>
/// Address model
/// </summary>
public class Address
{
    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("addressLine1")]
    public string AddressLine1 { get; set; } = string.Empty;

    [JsonPropertyName("addressLine2")]
    public string? AddressLine2 { get; set; }

    [JsonPropertyName("city")]
    public string City { get; set; } = string.Empty;

    [JsonPropertyName("districtOrCounty")]
    public string? DistrictOrCounty { get; set; }

    [JsonPropertyName("stateOrProvinceCode")]
    public string StateOrProvinceCode { get; set; } = string.Empty;

    [JsonPropertyName("countryCode")]
    public string CountryCode { get; set; } = string.Empty;

    [JsonPropertyName("postalCode")]
    public string PostalCode { get; set; } = string.Empty;

    [JsonPropertyName("phoneNumber")]
    public string PhoneNumber { get; set; } = string.Empty; // Required for v2024-03-20 API
}

/// <summary>
/// Money model
/// </summary>
public class Money
{
    [JsonPropertyName("CurrencyCode")]
    public string CurrencyCode { get; set; } = string.Empty;

    [JsonPropertyName("Value")]
    public decimal Value { get; set; }
}

/// <summary>
/// Prep details model
/// </summary>
public class PrepDetails
{
    [JsonPropertyName("PrepInstruction")]
    public string PrepInstruction { get; set; } = string.Empty;

    [JsonPropertyName("PrepOwner")]
    public string PrepOwner { get; set; } = string.Empty;
}

/// <summary>
/// Create inbound shipment response (v2024-03-20)
/// </summary>
public class CreateInboundShipmentResponse
{
    [JsonPropertyName("inboundPlanId")]
    public string InboundPlanId { get; set; } = string.Empty;

    [JsonPropertyName("operationId")]
    public string OperationId { get; set; } = string.Empty;

    [JsonPropertyName("shipments")]
    public List<ShipmentSummary> Shipments { get; set; } = new();
}

/// <summary>
/// Shipment summary model for v2024-03-20 API
/// </summary>
public class ShipmentSummary
{
    [JsonPropertyName("shipmentId")]
    public string ShipmentId { get; set; } = string.Empty;

    [JsonPropertyName("name")]
    public string Name { get; set; } = string.Empty;

    [JsonPropertyName("destinationFulfillmentCenterId")]
    public string DestinationFulfillmentCenterId { get; set; } = string.Empty;

    [JsonPropertyName("shipmentStatus")]
    public string ShipmentStatus { get; set; } = string.Empty;
}

/// <summary>
/// Response model for packing options (v2024-03-20)
/// </summary>
public class PackingOptionsResponse
{
    [JsonPropertyName("packingOptions")]
    public List<PackingOption> PackingOptions { get; set; } = new();

    [JsonPropertyName("operationId")]
    public string OperationId { get; set; } = string.Empty;
}

/// <summary>
/// Packing option model for v2024-03-20 API
/// </summary>
public class PackingOption
{
    [JsonPropertyName("packingOptionId")]
    public string PackingOptionId { get; set; } = string.Empty;

    [JsonPropertyName("packingGroupId")]
    public string PackingGroupId { get; set; } = string.Empty;

    [JsonPropertyName("shipments")]
    public List<ShipmentSummary> Shipments { get; set; } = new();
}

/// <summary>
/// Shipments list response wrapper
/// </summary>
public class ShipmentsListResponse
{
    [JsonPropertyName("ShipmentData")]
    public List<GetShipmentResponse> ShipmentData { get; set; } = new();
}

/// <summary>
/// Alternative shipments list response for direct array
/// </summary>
public class ShipmentsListDirectResponse : List<GetShipmentResponse>
{
}

/// <summary>
/// Shipment details response
/// </summary>
public class GetShipmentResponse
{
    [JsonPropertyName("ShipmentId")]
    public string ShipmentId { get; set; } = string.Empty;

    [JsonPropertyName("ShipmentName")]
    public string ShipmentName { get; set; } = string.Empty;

    [JsonPropertyName("ShipmentStatus")]
    public string ShipmentStatus { get; set; } = string.Empty;

    [JsonPropertyName("DestinationFulfillmentCenterId")]
    public string DestinationFulfillmentCenterId { get; set; } = string.Empty;

    [JsonPropertyName("ShipFromAddress")]
    public Address? ShipFromAddress { get; set; }

    [JsonPropertyName("ShipToAddress")]
    public Address? ShipToAddress { get; set; }

    [JsonPropertyName("LabelPrepPreference")]
    public string LabelPrepPreference { get; set; } = string.Empty;

    [JsonPropertyName("AreCasesRequired")]
    public bool AreCasesRequired { get; set; }

    [JsonPropertyName("ConfirmedNeedByDate")]
    public DateTime? ConfirmedNeedByDate { get; set; }

    [JsonPropertyName("BoxContentsSource")]
    public string? BoxContentsSource { get; set; }
}
