namespace SilvrBear_Amazon_Automation.Constants;

/// <summary>
/// Constants for shipment processing and Amazon API integration
/// </summary>
public static class ShipmentConstants
{
    /// <summary>
    /// Default ship from address for all shipments
    /// </summary>
    public static class ShipFromAddress
    {
        public const string Name = "Dipti Jadhav";
        public const string AddressLine1 = "Shop No. 4, Ground Floor, Sanskruti CHS";
        public const string AddressLine2 = "Behind St. Xavier School, Mandvi Pada Road";
        public const string City = "Kashigaon, Mira Road";
        public const string StateOrProvince = "Maharashtra";
        public const string PostalCode = "401107";
        public const string CountryCode = "IN";
        public const string Phone = "+91-9876543210"; // Add actual phone number
        public const string Email = "<EMAIL>"; // Add actual email
    }

    /// <summary>
    /// Amazon fulfillment center details for India
    /// </summary>
    public static class FulfillmentCenters
    {
        /// <summary>
        /// Default fulfillment center - ISK3 (India)
        /// </summary>
        public const string DefaultFulfillmentCenterId = "ISK3";
        public const string DefaultFulfillmentCenterName = "Amazon Fulfillment Center ISK3";
        
        /// <summary>
        /// Available fulfillment centers in India
        /// </summary>
        public static readonly Dictionary<string, string> IndiaFulfillmentCenters = new()
        {
            { "ISK3", "Amazon FC ISK3 - Mumbai" },
            { "ISK1", "Amazon FC ISK1 - Delhi" },
            { "ISK2", "Amazon FC ISK2 - Bangalore" },
            { "ISK4", "Amazon FC ISK4 - Chennai" },
            { "ISK5", "Amazon FC ISK5 - Hyderabad" }
        };
    }

    /// <summary>
    /// OpenAI API configuration
    /// </summary>
    public static class OpenAI
    {
        public const string VisionModel = "gpt-4-vision-preview";
        public const string TextModel = "gpt-4";
        public const int MaxTokens = 4000;
        public const double Temperature = 0.1; // Low temperature for consistent extraction
        
        /// <summary>
        /// System prompt for image processing
        /// </summary>
        public const string ImageProcessingPrompt = @"
You are an expert data extraction assistant. Analyze the handwritten table in the image and extract shipment box details.

The image contains a table with columns for:
- Box No. (Box number)
- Product Name (Product name)
- Barcode Type (old/new)
- Quantity (Number of items)
- Dimensions (Format: LxWxH like 58X38X26)
- Remarks (Weight information like '269 To 300')

Extract ALL visible rows from the table and return the data in the following JSON format:
{
  ""boxes"": [
    {
      ""boxNumber"": 1,
      ""productName"": ""Skinny Black"",
      ""barcodeType"": ""old"",
      ""quantity"": 200,
      ""dimensions"": ""58X38X26"",
      ""remarks"": ""269 To 300""
    }
  ]
}

Important rules:
1. Extract ONLY the product names that are clearly visible and readable
2. Use the EXACT product names from the provided list when possible
3. If a product name is unclear, use the closest match from the provided list
4. Include all visible boxes, even if some fields are empty
5. For dimensions, maintain the exact format (e.g., 58X38X26)
6. For remarks, extract weight ranges or any other notes
7. Return valid JSON only, no additional text or explanations
";
    }

    /// <summary>
    /// Amazon Seller API configuration for India
    /// </summary>
    public static class AmazonAPI
    {
        public const string IndiaMarketplaceId = "A21TJRUUN4KGV"; // Amazon India marketplace
        public const string IndiaRegion = "eu-west-1"; // Amazon India uses EU West region
        public const string IndiaBaseUrl = "https://sellingpartnerapi-eu.amazon.com";
        
        /// <summary>
        /// Default shipment preferences
        /// </summary>
        public const string DefaultLabelPrepPreference = "SELLER_LABEL";
        public const string DefaultShipmentType = "SP"; // Small Parcel
        public const bool DefaultAreCasesRequired = false;
        
        /// <summary>
        /// Booking preferences
        /// </summary>
        public const string PreferredTimeSlot = "EARLIEST_AVAILABLE";
        public const int BookingAdvanceDays = 1; // Book 1 day in advance
    }

    /// <summary>
    /// File processing constants
    /// </summary>
    public static class FileProcessing
    {
        public const int MaxImageSizeMB = 10;
        public const int MaxImageSizeBytes = MaxImageSizeMB * 1024 * 1024;
        
        public static readonly string[] SupportedImageFormats = { ".jpg", ".jpeg", ".png", ".bmp", ".gif" };
        public static readonly string[] SupportedExcelFormats = { ".xlsx", ".xls" };
        
        public const string MappingFileName = "Mapping.xlsx";
        public const string SampleImageFileName = "sample-image.jpg";
    }

    /// <summary>
    /// Weight and dimension conversion constants
    /// </summary>
    public static class Conversions
    {
        public const decimal CmToInches = 0.393701m;
        public const decimal GramsToPounds = 0.00220462m;
        public const decimal KgToPounds = 2.20462m;
        
        /// <summary>
        /// Default weights when not specified (in grams)
        /// </summary>
        public const decimal DefaultItemWeightGrams = 250m;
        public const decimal DefaultBoxWeightGrams = 100m; // Empty box weight
    }

    /// <summary>
    /// Validation constants
    /// </summary>
    public static class Validation
    {
        public const int MinBoxNumber = 1;
        public const int MaxBoxNumber = 999;
        public const int MinQuantity = 1;
        public const int MaxQuantity = 10000;
        public const decimal MinDimension = 0.1m;
        public const decimal MaxDimension = 200m; // 200 cm max
        public const decimal MinWeight = 0.01m;
        public const decimal MaxWeight = 50m; // 50 kg max per box
        
        public const int MaxProductNameLength = 100;
        public const int MaxRemarksLength = 500;
    }

    /// <summary>
    /// Error messages
    /// </summary>
    public static class ErrorMessages
    {
        public const string InvalidImageFormat = "Invalid image format. Supported formats: JPG, PNG, BMP, GIF";
        public const string ImageTooLarge = "Image size exceeds maximum limit of {0}MB";
        public const string NoBoxesFound = "No valid boxes found in the image";
        public const string ProductNotFound = "Product '{0}' not found in mapping file";
        public const string InvalidDimensions = "Invalid dimensions format. Expected format: LxWxH (e.g., 58X38X26)";
        public const string InvalidWeight = "Invalid weight format. Expected format: 'min To max' (e.g., 269 To 300)";
        public const string OpenAIProcessingFailed = "Failed to process image with OpenAI: {0}";
        public const string AmazonAPIFailed = "Amazon API call failed: {0}";
        public const string MappingFileNotFound = "Product mapping file not found: {0}";
    }

    /// <summary>
    /// Success messages
    /// </summary>
    public static class SuccessMessages
    {
        public const string ImageProcessedSuccessfully = "Image processed successfully. Found {0} boxes.";
        public const string ShipmentCreatedSuccessfully = "Inbound shipment created successfully. Shipment ID: {0}";
        public const string DataValidatedSuccessfully = "All data validated successfully. Ready to create shipment.";
        public const string MappingLoadedSuccessfully = "Product mapping loaded successfully. {0} products available.";
    }
}
