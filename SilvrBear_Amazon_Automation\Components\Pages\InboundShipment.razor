@page "/inbound-shipment"
@using SilvrBear_Amazon_Automation.Models
@using SilvrBear_Amazon_Automation.Services
@inject IInboundShipmentService InboundShipmentService
@inject ILogger<InboundShipment> Logger
@rendermode InteractiveServer

<PageTitle>Create Inbound Shipment</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <h1 class="mb-4">
                <i class="bi bi-box-seam"></i>
                Create Amazon FBA Inbound Shipment
            </h1>

            @if (!string.IsNullOrEmpty(statusMessage))
            {
                <div class="alert @(isSuccess ? "alert-success" : "alert-danger") alert-dismissible fade show" role="alert">
                    @statusMessage
                    <button type="button" class="btn-close" @onclick="ClearStatusMessage"></button>
                </div>
            }
        </div>
    </div>

    <div class="row">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Shipment Information</h5>
                </div>
                <div class="card-body">
                    <EditForm Model="shipmentRequest" OnValidSubmit="CreateShipment">
                        <DataAnnotationsValidator />

                        <!-- Basic Information -->
                        <div class="row mb-3">
                            <div class="col-md-6">
                                <label class="form-label">Shipment Name *</label>
                                <InputText @bind-Value="shipmentRequest.ShipmentName" class="form-control" placeholder="Enter shipment name" />
                                <ValidationMessage For="@(() => shipmentRequest.ShipmentName)" />
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Fulfillment Center *</label>
                                <InputSelect @bind-Value="shipmentRequest.DestinationFulfillmentCenterId" class="form-select">
                                    <option value="">Select Fulfillment Center</option>
                                    @foreach (var fc in fulfillmentCenters)
                                    {
                                        <option value="@fc.FulfillmentCenterId">@fc.Name (@fc.FulfillmentCenterId)</option>
                                    }
                                </InputSelect>
                                <ValidationMessage For="@(() => shipmentRequest.DestinationFulfillmentCenterId)" />
                            </div>
                        </div>

                        <!-- Items Section -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6>Items</h6>
                                <button type="button" class="btn btn-outline-primary btn-sm" @onclick="AddItem">
                                    <i class="bi bi-plus"></i> Add Item
                                </button>
                            </div>

                            @if (shipmentRequest.Items.Any())
                            {
                                <div class="table-responsive">
                                    <table class="table table-bordered">
                                        <thead class="table-light">
                                            <tr>
                                                <th>SKU *</th>
                                                <th>Product Name</th>
                                                <th>Quantity *</th>
                                                <th>ASIN</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @for (int i = 0; i < shipmentRequest.Items.Count; i++)
                                            {
                                                var index = i;
                                                <tr>
                                                    <td>
                                                        <InputText @bind-Value="shipmentRequest.Items[index].Sku" class="form-control form-control-sm" />
                                                    </td>
                                                    <td>
                                                        <InputText @bind-Value="shipmentRequest.Items[index].ProductName" class="form-control form-control-sm" />
                                                    </td>
                                                    <td>
                                                        <InputNumber @bind-Value="shipmentRequest.Items[index].Quantity" class="form-control form-control-sm" />
                                                    </td>
                                                    <td>
                                                        <InputText @bind-Value="shipmentRequest.Items[index].Asin" class="form-control form-control-sm" />
                                                    </td>
                                                    <td>
                                                        <button type="button" class="btn btn-outline-danger btn-sm" @onclick="() => RemoveItem(index)">
                                                            <i class="bi bi-trash"></i>
                                                        </button>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                            else
                            {
                                <div class="text-muted text-center py-3">
                                    No items added yet. Click "Add Item" to get started.
                                </div>
                            }
                        </div>

                        <!-- Boxes Section -->
                        <div class="mb-4">
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <h6>Boxes</h6>
                                <button type="button" class="btn btn-outline-primary btn-sm" @onclick="AddBox">
                                    <i class="bi bi-plus"></i> Add Box
                                </button>
                            </div>

                            @if (shipmentRequest.Boxes.Any())
                            {
                                @for (int i = 0; i < shipmentRequest.Boxes.Count; i++)
                                {
                                    var boxIndex = i;
                                    <div class="card mb-3">
                                        <div class="card-header d-flex justify-content-between align-items-center">
                                            <span>Box @(boxIndex + 1)</span>
                                            <button type="button" class="btn btn-outline-danger btn-sm" @onclick="() => RemoveBox(boxIndex)">
                                                <i class="bi bi-trash"></i>
                                            </button>
                                        </div>
                                        <div class="card-body">
                                            <div class="row">
                                                <div class="col-md-3">
                                                    <label class="form-label">Box ID *</label>
                                                    <InputText @bind-Value="shipmentRequest.Boxes[boxIndex].BoxId" class="form-control form-control-sm" />
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">Length (in) *</label>
                                                    <InputNumber @bind-Value="shipmentRequest.Boxes[boxIndex].Dimensions.Length" class="form-control form-control-sm" />
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">Width (in) *</label>
                                                    <InputNumber @bind-Value="shipmentRequest.Boxes[boxIndex].Dimensions.Width" class="form-control form-control-sm" />
                                                </div>
                                                <div class="col-md-2">
                                                    <label class="form-label">Height (in) *</label>
                                                    <InputNumber @bind-Value="shipmentRequest.Boxes[boxIndex].Dimensions.Height" class="form-control form-control-sm" />
                                                </div>
                                                <div class="col-md-3">
                                                    <label class="form-label">Weight (lbs) *</label>
                                                    <InputNumber @bind-Value="shipmentRequest.Boxes[boxIndex].Weight.Value" class="form-control form-control-sm" />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                }
                            }
                            else
                            {
                                <div class="text-muted text-center py-3">
                                    No boxes added yet. Click "Add Box" to specify packaging.
                                </div>
                            }
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary" disabled="@isLoading">
                                @if (isLoading)
                                {
                                    <span class="spinner-border spinner-border-sm me-2"></span>
                                    <text>Creating Shipment...</text>
                                }
                                else
                                {
                                    <i class="bi bi-send"></i>
                                    <text>Create Inbound Shipment</text>
                                }
                            </button>
                        </div>
                    </EditForm>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">Instructions</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> Getting Started</h6>
                        <ol class="mb-0">
                            <li>Enter a descriptive shipment name</li>
                            <li>Select the destination fulfillment center</li>
                            <li>Add your products with SKU and quantity</li>
                            <li>Specify box dimensions and weights</li>
                            <li>Click "Create Inbound Shipment"</li>
                        </ol>
                    </div>

                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> Sandbox Mode</h6>
                        <p class="mb-0">This application is configured for Amazon's sandbox environment. No real shipments will be created.</p>
                    </div>
                </div>
            </div>

            @if (lastCreatedShipment != null)
            {
                <div class="card mt-3">
                    <div class="card-header">
                        <h5 class="card-title mb-0">Last Created Shipment</h5>
                    </div>
                    <div class="card-body">
                        <dl class="row">
                            <dt class="col-sm-4">Shipment ID:</dt>
                            <dd class="col-sm-8"><code>@lastCreatedShipment.ShipmentId</code></dd>

                            <dt class="col-sm-4">Name:</dt>
                            <dd class="col-sm-8">@lastCreatedShipment.ShipmentName</dd>

                            <dt class="col-sm-4">Status:</dt>
                            <dd class="col-sm-8">
                                <span class="badge bg-primary">@lastCreatedShipment.Status</span>
                            </dd>

                            <dt class="col-sm-4">Items:</dt>
                            <dd class="col-sm-8">@lastCreatedShipment.Items.Count item(s)</dd>
                        </dl>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    private CreateInboundShipmentRequest shipmentRequest = new();
    private List<FulfillmentCenter> fulfillmentCenters = new();
    private InboundShipmentResponse? lastCreatedShipment;
    private bool isLoading = false;
    private string statusMessage = string.Empty;
    private bool isSuccess = false;

    protected override async Task OnInitializedAsync()
    {
        // Initialize with default values
        shipmentRequest.ShipmentName = $"Shipment-{DateTime.Now:yyyyMMdd-HHmmss}";
        shipmentRequest.LabelPrepPreference = "SELLER_LABEL";

        // Add one default item and box
        AddItem();
        AddBox();

        // Load fulfillment centers
        try
        {
            fulfillmentCenters = await InboundShipmentService.GetAvailableFulfillmentCentersAsync();
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error loading fulfillment centers");
            SetStatusMessage("Error loading fulfillment centers", false);
        }
    }

    private void AddItem()
    {
        shipmentRequest.Items.Add(new ShipmentItem
        {
            Condition = "NewItem",
            Quantity = 1
        });
    }

    private void RemoveItem(int index)
    {
        if (index >= 0 && index < shipmentRequest.Items.Count)
        {
            shipmentRequest.Items.RemoveAt(index);
        }
    }

    private void AddBox()
    {
        shipmentRequest.Boxes.Add(new ShipmentBox
        {
            BoxId = $"BOX-{shipmentRequest.Boxes.Count + 1:D3}",
            Dimensions = new BoxDimensions
            {
                Length = 12,
                Width = 9,
                Height = 6,
                Unit = "inches"
            },
            Weight = new BoxWeight
            {
                Value = 2.5m,
                Unit = "pounds"
            }
        });
    }

    private void RemoveBox(int index)
    {
        if (index >= 0 && index < shipmentRequest.Boxes.Count)
        {
            shipmentRequest.Boxes.RemoveAt(index);
        }
    }

    private async Task CreateShipment()
    {
        isLoading = true;
        ClearStatusMessage();

        try
        {
            Logger.LogInformation("Creating inbound shipment: {ShipmentName}", shipmentRequest.ShipmentName);

            var result = await InboundShipmentService.CreateCompleteInboundShipmentAsync(shipmentRequest);

            if (result.Errors.Any())
            {
                var errorMessage = string.Join("; ", result.Errors);
                SetStatusMessage($"Failed to create shipment: {errorMessage}", false);
                Logger.LogWarning("Shipment creation failed: {Errors}", errorMessage);
            }
            else
            {
                lastCreatedShipment = result;
                SetStatusMessage($"Shipment created successfully! Shipment ID: {result.ShipmentId}", true);
                Logger.LogInformation("Shipment created successfully: {ShipmentId}", result.ShipmentId);

                // Reset form for next shipment
                ResetForm();
            }
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Unexpected error creating shipment");
            SetStatusMessage($"Unexpected error: {ex.Message}", false);
        }
        finally
        {
            isLoading = false;
        }
    }

    private void ResetForm()
    {
        shipmentRequest = new CreateInboundShipmentRequest
        {
            ShipmentName = $"Shipment-{DateTime.Now:yyyyMMdd-HHmmss}",
            LabelPrepPreference = "SELLER_LABEL"
        };

        AddItem();
        AddBox();
    }

    private void SetStatusMessage(string message, bool success)
    {
        statusMessage = message;
        isSuccess = success;
    }

    private void ClearStatusMessage()
    {
        statusMessage = string.Empty;
    }
}
