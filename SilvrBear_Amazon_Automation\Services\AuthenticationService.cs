using Microsoft.Extensions.Options;
using SilvrBear_Amazon_Automation.Models;
using System.Text;
using System.Text.Json;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Service for handling Amazon API authentication
/// </summary>
public class AuthenticationService
{
    private readonly HttpClient _httpClient;
    private readonly AmazonCredentials _credentials;
    private readonly ILogger<AuthenticationService> _logger;
    private string? _cachedAccessToken;
    private DateTime _tokenExpiryTime;

    public AuthenticationService(
        HttpClient httpClient,
        IOptions<AmazonCredentials> credentials,
        ILogger<AuthenticationService> logger)
    {
        _httpClient = httpClient;
        _credentials = credentials.Value;
        _logger = logger;
    }

    /// <summary>
    /// Gets a valid access token, refreshing if necessary
    /// </summary>
    /// <returns>Access token</returns>
    public async Task<string> GetAccessTokenAsync()
    {
        // Check if we have a cached token that's still valid
        if (!string.IsNullOrEmpty(_cachedAccessToken) && DateTime.UtcNow < _tokenExpiryTime.AddMinutes(-5))
        {
            return _cachedAccessToken;
        }

        // Request a new token
        return await RefreshAccessTokenAsync();
    }

    /// <summary>
    /// Refreshes the access token using the refresh token
    /// </summary>
    /// <returns>New access token</returns>
    private async Task<string> RefreshAccessTokenAsync()
    {
        try
        {
            var requestBody = new Dictionary<string, string>
            {
                ["grant_type"] = "refresh_token",
                ["refresh_token"] = _credentials.RefreshToken,
                ["client_id"] = _credentials.ClientId,
                ["client_secret"] = _credentials.ClientSecret
            };

            var content = new FormUrlEncodedContent(requestBody);
            
            _logger.LogInformation("Requesting new access token from LWA");
            
            var response = await _httpClient.PostAsync(_credentials.LwaTokenUrl, content);
            
            if (!response.IsSuccessStatusCode)
            {
                var errorContent = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to get access token. Status: {StatusCode}, Content: {Content}", 
                    response.StatusCode, errorContent);
                throw new HttpRequestException($"Failed to get access token: {response.StatusCode}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<LwaTokenResponse>(responseContent);

            if (tokenResponse == null || string.IsNullOrEmpty(tokenResponse.AccessToken))
            {
                _logger.LogError("Invalid token response received");
                throw new InvalidOperationException("Invalid token response received");
            }

            // Cache the token
            _cachedAccessToken = tokenResponse.AccessToken;
            _tokenExpiryTime = DateTime.UtcNow.AddSeconds(tokenResponse.ExpiresIn);

            _logger.LogInformation("Successfully obtained new access token, expires at {ExpiryTime}", _tokenExpiryTime);

            return _cachedAccessToken;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing access token");
            throw;
        }
    }

    /// <summary>
    /// Creates authorization headers for Amazon API requests
    /// </summary>
    /// <returns>Dictionary of headers</returns>
    public async Task<Dictionary<string, string>> GetAuthorizationHeadersAsync()
    {
        var accessToken = await GetAccessTokenAsync();
        
        return new Dictionary<string, string>
        {
            ["Authorization"] = $"Bearer {accessToken}",
            ["x-amz-access-token"] = accessToken,
            ["x-amz-date"] = DateTime.UtcNow.ToString("yyyyMMddTHHmmssZ"),
            ["Content-Type"] = "application/json"
        };
    }

    /// <summary>
    /// Validates that all required credentials are configured
    /// </summary>
    /// <returns>True if credentials are valid</returns>
    public bool ValidateCredentials()
    {
        var isValid = !string.IsNullOrEmpty(_credentials.ClientId) &&
                     !string.IsNullOrEmpty(_credentials.ClientSecret) &&
                     !string.IsNullOrEmpty(_credentials.RefreshToken) &&
                     !string.IsNullOrEmpty(_credentials.MarketplaceId) &&
                     !string.IsNullOrEmpty(_credentials.SellerId);

        if (!isValid)
        {
            _logger.LogWarning("Amazon credentials validation failed. Please check configuration.");
        }

        return isValid;
    }

    /// <summary>
    /// Clears the cached access token (useful for testing or error recovery)
    /// </summary>
    public void ClearCachedToken()
    {
        _cachedAccessToken = null;
        _tokenExpiryTime = DateTime.MinValue;
        _logger.LogInformation("Cached access token cleared");
    }
}
