using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// High-level service for managing inbound shipments
/// </summary>
public class InboundShipmentService : IInboundShipmentService
{
    private readonly IAmazonSellerApiService _amazonApiService;
    private readonly ILogger<InboundShipmentService> _logger;

    public InboundShipmentService(
        IAmazonSellerApiService amazonApiService,
        ILogger<InboundShipmentService> logger)
    {
        _amazonApiService = amazonApiService;
        _logger = logger;
    }

    public async Task<InboundShipmentResponse> CreateCompleteInboundShipmentAsync(CreateInboundShipmentRequest request)
    {
        try
        {
            _logger.LogInformation("Starting complete inbound shipment creation for {ShipmentName}", request.ShipmentName);

            // Step 1: Validate the request
            var (isValid, errors) = await ValidateShipmentRequestAsync(request);
            if (!isValid)
            {
                return new InboundShipmentResponse
                {
                    Errors = errors,
                    ShipmentName = request.ShipmentName
                };
            }

            // Step 2: Create shipment plan with India ship-from address
            var shipFromAddress = new Address
            {
                Name = "Dipti Jadhav",
                AddressLine1 = "Shop No. 4, Ground Floor, Sanskruti CHS",
                AddressLine2 = "Behind St. Xavier School, Mandvi Pada Road",
                City = "Kashigaon, Mira Road",
                StateOrProvinceCode = "MH", // Maharashtra state code
                CountryCode = "IN", // India country code
                PostalCode = "401107"
            };

            var planResponse = await _amazonApiService.CreateInboundShipmentPlanAsync(
                request.Items,
                shipFromAddress,
                request.LabelPrepPreference ?? "SELLER_LABEL");

            if (!planResponse.IsSuccess || string.IsNullOrEmpty(planResponse.Payload?.InboundPlanId))
            {
                return new InboundShipmentResponse
                {
                    Errors = planResponse.Errors.Select(e => e.Message).ToList(),
                    ShipmentName = request.ShipmentName
                };
            }

            // Step 3: Extract inbound plan ID from the new v2024-03-20 API response
            var inboundPlanId = planResponse.Payload.InboundPlanId;

            _logger.LogInformation("Created inbound plan with ID: {InboundPlanId}", inboundPlanId);

            // Step 4: Create the actual shipment using the new workflow
            var createResponse = await _amazonApiService.CreateInboundShipmentAsync(inboundPlanId, request);

            if (!createResponse.IsSuccess)
            {
                return new InboundShipmentResponse
                {
                    Errors = createResponse.Errors.Select(e => e.Message).ToList(),
                    ShipmentName = request.ShipmentName
                };
            }

            // Step 5: Extract shipment ID from the response (use first shipment if multiple)
            var shipmentId = createResponse.Payload?.Shipments?.FirstOrDefault()?.ShipmentId ?? inboundPlanId;

            // Step 6: Get shipment details to return complete information (if available)
            var detailsResponse = await _amazonApiService.GetInboundShipmentAsync(shipmentId);

            return new InboundShipmentResponse
            {
                ShipmentId = shipmentId,
                ShipmentName = request.ShipmentName,
                Status = ShipmentStatus.WORKING,
                DestinationFulfillmentCenterId = request.DestinationFulfillmentCenterId,
                Items = request.Items,
                CreatedDate = DateTime.UtcNow,
                LastUpdatedDate = DateTime.UtcNow,
                InboundPlanId = inboundPlanId // Store the plan ID for future operations
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating complete inbound shipment");
            return new InboundShipmentResponse
            {
                Errors = new List<string> { $"Unexpected error: {ex.Message}" },
                ShipmentName = request.ShipmentName
            };
        }
    }

    public async Task<InboundShipmentResponse> GetShipmentDetailsAsync(string shipmentId)
    {
        try
        {
            var response = await _amazonApiService.GetInboundShipmentAsync(shipmentId);

            if (!response.IsSuccess || response.Payload == null)
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = response.Errors.Select(e => e.Message).ToList()
                };
            }

            var shipment = response.Payload;

            return new InboundShipmentResponse
            {
                ShipmentId = shipment.ShipmentId,
                ShipmentName = shipment.ShipmentName,
                Status = Enum.TryParse<ShipmentStatus>(shipment.ShipmentStatus, out var status) ? status : ShipmentStatus.WORKING,
                DestinationFulfillmentCenterId = shipment.DestinationFulfillmentCenterId,
                AreCasesRequired = shipment.AreCasesRequired,
                LabelPrepTypes = new List<string> { shipment.LabelPrepPreference }
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipment details for {ShipmentId}", shipmentId);
            return new InboundShipmentResponse
            {
                ShipmentId = shipmentId,
                Errors = new List<string> { $"Error retrieving shipment: {ex.Message}" }
            };
        }
    }

    public async Task<List<InboundShipmentResponse>> GetShipmentsAsync(
        ShipmentStatus? status = null,
        DateTime? fromDate = null,
        DateTime? toDate = null)
    {
        try
        {
            var statusList = status.HasValue ? new List<string> { status.Value.ToString() } : null;

            var response = await _amazonApiService.GetInboundShipmentsAsync(
                statusList,
                null,
                fromDate,
                toDate);

            if (!response.IsSuccess || response.Payload == null)
            {
                _logger.LogWarning("Failed to get shipments: {Errors}",
                    string.Join(", ", response.Errors.Select(e => e.Message)));
                return new List<InboundShipmentResponse>();
            }

            return response.Payload.Select(shipment => new InboundShipmentResponse
            {
                ShipmentId = shipment.ShipmentId,
                ShipmentName = shipment.ShipmentName,
                Status = Enum.TryParse<ShipmentStatus>(shipment.ShipmentStatus, out var s) ? s : ShipmentStatus.WORKING,
                DestinationFulfillmentCenterId = shipment.DestinationFulfillmentCenterId,
                AreCasesRequired = shipment.AreCasesRequired
            }).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipments list");
            return new List<InboundShipmentResponse>();
        }
    }

    public async Task<InboundShipmentResponse> UpdateShipmentBoxesAsync(string shipmentId, List<ShipmentBox> boxes)
    {
        try
        {
            // First get current shipment details
            var currentShipment = await GetShipmentDetailsAsync(shipmentId);
            if (currentShipment.Errors.Any())
            {
                return currentShipment;
            }

            // Create update request with box information
            var updateRequest = new CreateInboundShipmentRequest
            {
                ShipmentName = currentShipment.ShipmentName ?? $"Shipment-{shipmentId}",
                DestinationFulfillmentCenterId = currentShipment.DestinationFulfillmentCenterId ?? string.Empty,
                Items = currentShipment.Items,
                Boxes = boxes
            };

            // For the new v2024-03-20 API, we need the inbound plan ID to update packing information
            if (string.IsNullOrEmpty(currentShipment.InboundPlanId))
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = new List<string> { "Cannot update shipment: Inbound plan ID not available" }
                };
            }

            var response = await _amazonApiService.UpdateInboundShipmentAsync(currentShipment.InboundPlanId, updateRequest);

            if (!response.IsSuccess)
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = response.Errors.Select(e => e.Message).ToList()
                };
            }

            // Return updated shipment details
            return await GetShipmentDetailsAsync(shipmentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating shipment boxes for {ShipmentId}", shipmentId);
            return new InboundShipmentResponse
            {
                ShipmentId = shipmentId,
                Errors = new List<string> { $"Error updating boxes: {ex.Message}" }
            };
        }
    }

    public async Task<InboundShipmentResponse> ConfirmShipmentAsync(string shipmentId)
    {
        try
        {
            // For the new v2024-03-20 API, we need the inbound plan ID to confirm transportation
            // First get shipment details to extract the plan ID
            var shipmentDetails = await GetShipmentDetailsAsync(shipmentId);
            if (shipmentDetails.Errors.Any() || string.IsNullOrEmpty(shipmentDetails.InboundPlanId))
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = new List<string> { "Cannot confirm shipment: Unable to retrieve inbound plan ID" }
                };
            }

            // Generate and confirm transportation options using the new API
            var generateResponse = await _amazonApiService.GenerateTransportationOptionsAsync(shipmentDetails.InboundPlanId);
            if (!generateResponse.IsSuccess)
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = generateResponse.Errors.Select(e => e.Message).ToList()
                };
            }

            // List transportation options
            var listResponse = await _amazonApiService.ListTransportationOptionsAsync(shipmentDetails.InboundPlanId);
            if (!listResponse.IsSuccess)
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = listResponse.Errors.Select(e => e.Message).ToList()
                };
            }

            // For now, we'll assume the first transportation option is selected
            // In a real implementation, you might want to let the user choose
            var confirmResponse = await _amazonApiService.ConfirmTransportationOptionsAsync(
                shipmentDetails.InboundPlanId, "default-transport-option");

            if (!confirmResponse.IsSuccess)
            {
                return new InboundShipmentResponse
                {
                    ShipmentId = shipmentId,
                    Errors = confirmResponse.Errors.Select(e => e.Message).ToList()
                };
            }

            // Return updated shipment details
            return await GetShipmentDetailsAsync(shipmentId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming shipment {ShipmentId}", shipmentId);
            return new InboundShipmentResponse
            {
                ShipmentId = shipmentId,
                Errors = new List<string> { $"Error confirming shipment: {ex.Message}" }
            };
        }
    }

    public async Task<string> GetShippingLabelsAsync(string shipmentId, string labelType = "UNIQUE")
    {
        try
        {
            var response = await _amazonApiService.GetLabelsAsync(shipmentId, "PackageLabel_Letter_6", labelType);

            if (!response.IsSuccess)
            {
                _logger.LogWarning("Failed to get labels for shipment {ShipmentId}: {Errors}",
                    shipmentId, string.Join(", ", response.Errors.Select(e => e.Message)));
                return string.Empty;
            }

            // In a real implementation, you would extract the PDF data from the response
            // For now, return a placeholder
            return "BASE64_ENCODED_PDF_DATA_PLACEHOLDER";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting shipping labels for {ShipmentId}", shipmentId);
            return string.Empty;
        }
    }

    public Task<(bool IsValid, List<string> Errors)> ValidateShipmentRequestAsync(CreateInboundShipmentRequest request)
    {
        var errors = new List<string>();

        // Validate basic information
        if (string.IsNullOrWhiteSpace(request.ShipmentName))
            errors.Add("Shipment name is required");

        if (string.IsNullOrWhiteSpace(request.DestinationFulfillmentCenterId))
            errors.Add("Destination fulfillment center ID is required");

        // Validate items
        if (!request.Items?.Any() == true)
        {
            errors.Add("At least one item is required");
        }
        else
        {
            for (int i = 0; i < request.Items.Count; i++)
            {
                var item = request.Items[i];
                if (string.IsNullOrWhiteSpace(item.Sku))
                    errors.Add($"Item {i + 1}: SKU is required");
                if (item.Quantity <= 0)
                    errors.Add($"Item {i + 1}: Quantity must be greater than 0");
            }
        }

        // Validate boxes if provided
        if (request.Boxes?.Any() == true)
        {
            for (int i = 0; i < request.Boxes.Count; i++)
            {
                var box = request.Boxes[i];
                if (string.IsNullOrWhiteSpace(box.BoxId))
                    errors.Add($"Box {i + 1}: Box ID is required");

                if (box.Dimensions?.Length <= 0 || box.Dimensions?.Width <= 0 || box.Dimensions?.Height <= 0)
                    errors.Add($"Box {i + 1}: All dimensions must be greater than 0");

                if (box.Weight?.Value <= 0)
                    errors.Add($"Box {i + 1}: Weight must be greater than 0");
            }
        }

        return Task.FromResult((errors.Count == 0, errors));
    }

    public async Task<List<FulfillmentCenter>> GetAvailableFulfillmentCentersAsync()
    {
        // In a real implementation, this would call an Amazon API to get fulfillment centers
        // For now, return common India fulfillment centers
        await Task.Delay(1); // Simulate async operation

        return new List<FulfillmentCenter>
        {
            new FulfillmentCenter
            {
                FulfillmentCenterId = "ISK3",
                Name = "Amazon Fulfillment Center ISK3 - Mumbai",
                Address = "Amazon Fulfillment Center",
                City = "Mumbai",
                StateOrProvince = "Maharashtra",
                CountryCode = "IN",
                PostalCode = "400001"
            },
            new FulfillmentCenter
            {
                FulfillmentCenterId = "ISK1",
                Name = "Amazon Fulfillment Center ISK1 - Delhi",
                Address = "Amazon Fulfillment Center",
                City = "Delhi",
                StateOrProvince = "Delhi",
                CountryCode = "IN",
                PostalCode = "110001"
            },
            new FulfillmentCenter
            {
                FulfillmentCenterId = "ISK2",
                Name = "Amazon Fulfillment Center ISK2 - Bangalore",
                Address = "Amazon Fulfillment Center",
                City = "Bangalore",
                StateOrProvince = "Karnataka",
                CountryCode = "IN",
                PostalCode = "560001"
            },
            new FulfillmentCenter
            {
                FulfillmentCenterId = "ISK4",
                Name = "Amazon Fulfillment Center ISK4 - Chennai",
                Address = "Amazon Fulfillment Center",
                City = "Chennai",
                StateOrProvince = "Tamil Nadu",
                CountryCode = "IN",
                PostalCode = "600001"
            },
            new FulfillmentCenter
            {
                FulfillmentCenterId = "ISK5",
                Name = "Amazon Fulfillment Center ISK5 - Hyderabad",
                Address = "Amazon Fulfillment Center",
                City = "Hyderabad",
                StateOrProvince = "Telangana",
                CountryCode = "IN",
                PostalCode = "500001"
            }
        };
    }

    public async Task<decimal?> EstimateShippingCostAsync(string shipmentId)
    {
        try
        {
            // For the new v2024-03-20 API, shipping cost estimation is part of transportation options
            // First get shipment details to extract the plan ID
            var shipmentDetails = await GetShipmentDetailsAsync(shipmentId);
            if (shipmentDetails.Errors.Any() || string.IsNullOrEmpty(shipmentDetails.InboundPlanId))
            {
                _logger.LogWarning("Cannot estimate shipping cost for {ShipmentId}: Unable to retrieve inbound plan ID", shipmentId);
                return null;
            }

            // Generate transportation options to get cost estimates
            var generateResponse = await _amazonApiService.GenerateTransportationOptionsAsync(shipmentDetails.InboundPlanId);
            if (!generateResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to generate transportation options for {ShipmentId}: {Errors}",
                    shipmentId, string.Join(", ", generateResponse.Errors.Select(e => e.Message)));
                return null;
            }

            // List transportation options to get cost information
            var listResponse = await _amazonApiService.ListTransportationOptionsAsync(shipmentDetails.InboundPlanId);
            if (!listResponse.IsSuccess)
            {
                _logger.LogWarning("Failed to list transportation options for {ShipmentId}: {Errors}",
                    shipmentId, string.Join(", ", listResponse.Errors.Select(e => e.Message)));
                return null;
            }

            // In a real implementation, you would extract the cost from the transportation options response
            // For now, return a placeholder estimate based on the new API workflow
            return 25.99m;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating shipping cost for {ShipmentId}", shipmentId);
            return null;
        }
    }

    public async Task<bool> CancelShipmentAsync(string shipmentId)
    {
        try
        {
            // Get current shipment status
            var shipment = await GetShipmentDetailsAsync(shipmentId);

            if (shipment.Errors.Any())
            {
                _logger.LogWarning("Cannot cancel shipment {ShipmentId}: {Errors}",
                    shipmentId, string.Join(", ", shipment.Errors));
                return false;
            }

            // Check if shipment can be cancelled
            if (shipment.Status == ShipmentStatus.SHIPPED ||
                shipment.Status == ShipmentStatus.RECEIVING ||
                shipment.Status == ShipmentStatus.CANCELLED ||
                shipment.Status == ShipmentStatus.DELETED)
            {
                _logger.LogWarning("Cannot cancel shipment {ShipmentId} in status {Status}",
                    shipmentId, shipment.Status);
                return false;
            }

            // In a real implementation, you would call the Amazon API to cancel the shipment
            // For now, just log the cancellation attempt
            _logger.LogInformation("Shipment {ShipmentId} cancellation requested", shipmentId);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cancelling shipment {ShipmentId}", shipmentId);
            return false;
        }
    }
}