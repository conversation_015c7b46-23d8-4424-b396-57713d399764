using SilvrBear_Amazon_Automation.Components;
using SilvrBear_Amazon_Automation.Models;
using SilvrBear_Amazon_Automation.Services;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents()
    .AddInteractiveServerComponents();

// Configure Amazon credentials
builder.Services.Configure<AmazonCredentials>(
    builder.Configuration.GetSection("AmazonCredentials"));

// Add HTTP client services
builder.Services.AddHttpClient<AuthenticationService>();
builder.Services.AddHttpClient<AmazonApiClient>();

// Register application services
builder.Services.AddScoped<AuthenticationService>();
builder.Services.AddScoped<AmazonApiClient>();
builder.Services.AddScoped<IAmazonSellerApiService, AmazonSellerApiService>();
builder.Services.AddScoped<IInboundShipmentService, InboundShipmentService>();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();

app.UseStaticFiles();
app.UseAntiforgery();

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode();

app.Run();
