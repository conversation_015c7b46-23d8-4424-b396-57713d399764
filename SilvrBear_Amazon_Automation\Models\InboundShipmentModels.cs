using System.ComponentModel.DataAnnotations;

namespace SilvrBear_Amazon_Automation.Models;

/// <summary>
/// Request model for creating an inbound shipment
/// </summary>
public class CreateInboundShipmentRequest
{
    [Required]
    public string ShipmentName { get; set; } = string.Empty;

    [Required]
    public string DestinationFulfillmentCenterId { get; set; } = string.Empty;

    [Required]
    public List<ShipmentItem> Items { get; set; } = new();

    [Required]
    public List<ShipmentBox> Boxes { get; set; } = new();

    public string? LabelPrepPreference { get; set; } = "SELLER_LABEL";
    public string? ShipFromAddress { get; set; }
    public DateTime? IntendedBoxContentsSource { get; set; }
}

/// <summary>
/// Represents an item in the shipment
/// </summary>
public class ShipmentItem
{
    [Required]
    public string Sku { get; set; } = string.Empty;

    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "Quantity must be greater than 0")]
    public int Quantity { get; set; }

    public string? Asin { get; set; }
    public string? FnSku { get; set; }
    public string? ProductName { get; set; }
    public string? Condition { get; set; } = "NewItem";
    public decimal? UnitCost { get; set; }
}

/// <summary>
/// Represents a box in the shipment
/// </summary>
public class ShipmentBox
{
    [Required]
    public string BoxId { get; set; } = string.Empty;

    [Required]
    public BoxDimensions Dimensions { get; set; } = new();

    [Required]
    public BoxWeight Weight { get; set; } = new();

    public List<BoxContent> Contents { get; set; } = new();
}

/// <summary>
/// Box dimensions in inches
/// </summary>
public class BoxDimensions
{
    [Required]
    [Range(0.1, double.MaxValue, ErrorMessage = "Length must be greater than 0")]
    public decimal Length { get; set; }

    [Required]
    [Range(0.1, double.MaxValue, ErrorMessage = "Width must be greater than 0")]
    public decimal Width { get; set; }

    [Required]
    [Range(0.1, double.MaxValue, ErrorMessage = "Height must be greater than 0")]
    public decimal Height { get; set; }

    public string Unit { get; set; } = "inches";
}

/// <summary>
/// Box weight
/// </summary>
public class BoxWeight
{
    [Required]
    [Range(0.1, double.MaxValue, ErrorMessage = "Weight must be greater than 0")]
    public decimal Value { get; set; }

    public string Unit { get; set; } = "pounds";
}

/// <summary>
/// Contents of a box
/// </summary>
public class BoxContent
{
    [Required]
    public string Sku { get; set; } = string.Empty;

    [Required]
    [Range(1, int.MaxValue, ErrorMessage = "Quantity must be greater than 0")]
    public int Quantity { get; set; }
}

/// <summary>
/// Fulfillment center information
/// </summary>
public class FulfillmentCenter
{
    public string FulfillmentCenterId { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Address { get; set; } = string.Empty;
    public string City { get; set; } = string.Empty;
    public string StateOrProvince { get; set; } = string.Empty;
    public string CountryCode { get; set; } = string.Empty;
    public string PostalCode { get; set; } = string.Empty;
}

/// <summary>
/// Shipment status enumeration
/// </summary>
public enum ShipmentStatus
{
    WORKING,
    SHIPPED,
    RECEIVING,
    CANCELLED,
    DELETED,
    CLOSED,
    ERROR,
    IN_TRANSIT,
    DELIVERED,
    CHECKED_IN
}

/// <summary>
/// Consolidated item details for shipment (unique SKUs with total quantities)
/// </summary>
public class ConsolidatedItemDetails
{
    public string Sku { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public int TotalQuantity { get; set; }
    public decimal UnitWeightKg { get; set; }
    public decimal TotalWeightKg { get; set; }
    public string? BarcodeType { get; set; }
    public List<int> SourceBoxNumbers { get; set; } = new();
}

/// <summary>
/// Box details with calculated weights and contents
/// </summary>
public class ConsolidatedBoxDetails
{
    public int BoxNumber { get; set; }
    public string BoxId { get; set; } = string.Empty;
    public decimal TotalWeightKg { get; set; }
    public decimal TotalWeightPounds { get; set; }
    public BoxDimensions Dimensions { get; set; } = new();
    public List<BoxItemSummary> Items { get; set; } = new();
    public string DimensionsString { get; set; } = string.Empty;
}

/// <summary>
/// Summary of items in a box
/// </summary>
public class BoxItemSummary
{
    public string Sku { get; set; } = string.Empty;
    public string ProductName { get; set; } = string.Empty;
    public int Quantity { get; set; }
    public decimal UnitWeightKg { get; set; }
    public decimal TotalWeightKg { get; set; }
}

/// <summary>
/// Response model for inbound shipment operations
/// </summary>
public class InboundShipmentResponse
{
    public string? ShipmentId { get; set; }
    public string? InboundPlanId { get; set; } // Added for v2024-03-20 API support
    public string? ShipmentName { get; set; }
    public ShipmentStatus Status { get; set; }
    public string? DestinationFulfillmentCenterId { get; set; }
    public List<string> LabelPrepTypes { get; set; } = new();
    public bool AreCasesRequired { get; set; }
    public DateTime? CreatedDate { get; set; }
    public DateTime? LastUpdatedDate { get; set; }
    public List<ShipmentItem> Items { get; set; } = new();
    public List<string> Errors { get; set; } = new();
}
