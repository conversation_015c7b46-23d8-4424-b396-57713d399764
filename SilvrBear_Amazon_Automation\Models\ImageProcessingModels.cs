using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace SilvrBear_Amazon_Automation.Models;

/// <summary>
/// Request model for image processing
/// </summary>
public class ImageProcessingRequest
{
    [Required]
    public IFormFile ImageFile { get; set; } = null!;
    
    public string? ShipmentName { get; set; }
    public string? FulfillmentCenterId { get; set; }
    public List<string> AvailableProductNames { get; set; } = new();
}

/// <summary>
/// Response from OpenAI image processing
/// </summary>
public class OpenAIImageResponse
{
    [JsonPropertyName("boxes")]
    public List<ExtractedBoxData> Boxes { get; set; } = new();
}

/// <summary>
/// Box data extracted from image by OpenAI
/// </summary>
public class ExtractedBoxData
{
    [JsonPropertyName("boxNumber")]
    public int BoxNumber { get; set; }
    
    [JsonPropertyName("productName")]
    public string ProductName { get; set; } = string.Empty;
    
    [JsonPropertyName("barcodeType")]
    public string? BarcodeType { get; set; }
    
    [JsonPropertyName("quantity")]
    public int Quantity { get; set; }
    
    [JsonPropertyName("dimensions")]
    public string Dimensions { get; set; } = string.Empty;
    
    [JsonPropertyName("remarks")]
    public string? Remarks { get; set; }
}

/// <summary>
/// Result of image processing operation
/// </summary>
public class ImageProcessingResult
{
    public bool IsSuccess { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public List<ProcessedBoxData> ProcessedBoxes { get; set; } = new();
    public string? ProcessedImagePath { get; set; }
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
    public TimeSpan ProcessingTime { get; set; }
}

/// <summary>
/// Processed box data with mapping and calculations
/// </summary>
public class ProcessedBoxData
{
    public int BoxNumber { get; set; }
    public string OriginalProductName { get; set; } = string.Empty;
    public string MappedProductName { get; set; } = string.Empty;
    public string? BarcodeType { get; set; }
    public int Quantity { get; set; }
    public string DimensionsString { get; set; } = string.Empty;
    public string? Remarks { get; set; }
    
    // Calculated fields
    public string GeneratedSKU { get; set; } = string.Empty;
    public decimal ItemWeightKg { get; set; }
    public decimal TotalItemWeightKg { get; set; }
    public decimal BoxWeightKg { get; set; }
    public decimal TotalBoxWeightKg { get; set; }
    
    // Parsed dimensions
    public BoxDimensions ParsedDimensions { get; set; } = new();
    
    // Validation
    public bool IsValid { get; set; } = true;
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> ValidationWarnings { get; set; } = new();
}

/// <summary>
/// Product mapping from Excel file
/// </summary>
public class ProductMapping
{
    public string ProductName { get; set; } = string.Empty;
    public string SKU { get; set; } = string.Empty;
    public string? BarcodeType { get; set; }
    public decimal WeightKg { get; set; }
    public string? Category { get; set; }
    public string? Description { get; set; }
    public bool IsActive { get; set; } = true;
}

/// <summary>
/// Complete product mapping data
/// </summary>
public class ProductMappingData
{
    public List<ProductMapping> Products { get; set; } = new();
    public Dictionary<string, ProductMapping> ProductLookup { get; set; } = new();
    public Dictionary<string, List<ProductMapping>> SKULookup { get; set; } = new();
    public DateTime LoadedAt { get; set; } = DateTime.UtcNow;
    public string SourceFile { get; set; } = string.Empty;
}

/// <summary>
/// OpenAI API request model
/// </summary>
public class OpenAIRequest
{
    [JsonPropertyName("model")]
    public string Model { get; set; } = "gpt-4o";
    
    [JsonPropertyName("messages")]
    public List<OpenAIMessage> Messages { get; set; } = new();
    
    [JsonPropertyName("max_tokens")]
    public int MaxTokens { get; set; } = 4000;
    
    [JsonPropertyName("temperature")]
    public double Temperature { get; set; } = 0.1;
}

/// <summary>
/// OpenAI message model
/// </summary>
public class OpenAIMessage
{
    [JsonPropertyName("role")]
    public string Role { get; set; } = string.Empty;
    
    [JsonPropertyName("content")]
    public List<OpenAIContent> Content { get; set; } = new();
}

/// <summary>
/// OpenAI content model (text or image)
/// </summary>
public class OpenAIContent
{
    [JsonPropertyName("type")]
    public string Type { get; set; } = string.Empty;
    
    [JsonPropertyName("text")]
    public string? Text { get; set; }
    
    [JsonPropertyName("image_url")]
    public OpenAIImageUrl? ImageUrl { get; set; }
}

/// <summary>
/// OpenAI image URL model
/// </summary>
public class OpenAIImageUrl
{
    [JsonPropertyName("url")]
    public string Url { get; set; } = string.Empty;
}

/// <summary>
/// OpenAI API response model
/// </summary>
public class OpenAIResponse
{
    [JsonPropertyName("id")]
    public string Id { get; set; } = string.Empty;
    
    [JsonPropertyName("object")]
    public string Object { get; set; } = string.Empty;
    
    [JsonPropertyName("created")]
    public long Created { get; set; }
    
    [JsonPropertyName("model")]
    public string Model { get; set; } = string.Empty;
    
    [JsonPropertyName("choices")]
    public List<OpenAIChoice> Choices { get; set; } = new();
    
    [JsonPropertyName("usage")]
    public OpenAIUsage? Usage { get; set; }
}

/// <summary>
/// OpenAI choice model
/// </summary>
public class OpenAIChoice
{
    [JsonPropertyName("index")]
    public int Index { get; set; }
    
    [JsonPropertyName("message")]
    public OpenAIResponseMessage Message { get; set; } = new();
    
    [JsonPropertyName("finish_reason")]
    public string FinishReason { get; set; } = string.Empty;
}

/// <summary>
/// OpenAI response message model
/// </summary>
public class OpenAIResponseMessage
{
    [JsonPropertyName("role")]
    public string Role { get; set; } = string.Empty;
    
    [JsonPropertyName("content")]
    public string Content { get; set; } = string.Empty;
}

/// <summary>
/// OpenAI usage statistics
/// </summary>
public class OpenAIUsage
{
    [JsonPropertyName("prompt_tokens")]
    public int PromptTokens { get; set; }
    
    [JsonPropertyName("completion_tokens")]
    public int CompletionTokens { get; set; }
    
    [JsonPropertyName("total_tokens")]
    public int TotalTokens { get; set; }
}

/// <summary>
/// Image validation result
/// </summary>
public class ImageValidationResult
{
    public bool IsValid { get; set; }
    public List<string> Errors { get; set; } = new();
    public long FileSizeBytes { get; set; }
    public string FileExtension { get; set; } = string.Empty;
    public string MimeType { get; set; } = string.Empty;
}

/// <summary>
/// Complete shipment creation request from image
/// </summary>
public class ImageToShipmentRequest
{
    public IFormFile ImageFile { get; set; } = null!;
    public string? ShipmentName { get; set; }
    public string? FulfillmentCenterId { get; set; }
    public bool AutoCreateShipment { get; set; } = false;
    public bool ValidateOnly { get; set; } = false;
}

/// <summary>
/// Editable box data row for JSON table editor
/// </summary>
public class BoxDataRow
{
    public int BoxNumber { get; set; }
    public string ProductName { get; set; } = string.Empty;
    public string? BarcodeType { get; set; }
    public int Quantity { get; set; }
    public string Dimensions { get; set; } = string.Empty;
    public string? Remarks { get; set; }

    // Validation properties
    public bool IsValid { get; set; } = true;
    public List<string> ValidationErrors { get; set; } = new();
    public List<string> ValidationWarnings { get; set; } = new();

    // UI state properties
    public bool IsEditing { get; set; } = false;
    public bool HasChanges { get; set; } = false;

    /// <summary>
    /// Creates a BoxDataRow from ExtractedBoxData
    /// </summary>
    public static BoxDataRow FromExtractedBoxData(ExtractedBoxData extracted)
    {
        return new BoxDataRow
        {
            BoxNumber = extracted.BoxNumber,
            ProductName = extracted.ProductName,
            BarcodeType = extracted.BarcodeType,
            Quantity = extracted.Quantity,
            Dimensions = extracted.Dimensions,
            Remarks = extracted.Remarks
        };
    }

    /// <summary>
    /// Converts to ExtractedBoxData for processing
    /// </summary>
    public ExtractedBoxData ToExtractedBoxData()
    {
        return new ExtractedBoxData
        {
            BoxNumber = BoxNumber,
            ProductName = ProductName,
            BarcodeType = BarcodeType,
            Quantity = Quantity,
            Dimensions = Dimensions,
            Remarks = Remarks
        };
    }

    /// <summary>
    /// Validates the box data row
    /// </summary>
    public void Validate()
    {
        ValidationErrors.Clear();
        ValidationWarnings.Clear();

        if (string.IsNullOrWhiteSpace(ProductName))
            ValidationErrors.Add("Product name is required");

        if (Quantity <= 0)
            ValidationErrors.Add("Quantity must be greater than 0");

        if (string.IsNullOrWhiteSpace(Dimensions))
            ValidationWarnings.Add("Dimensions not specified");

        if (string.IsNullOrWhiteSpace(BarcodeType))
            ValidationWarnings.Add("Barcode type not specified");

        IsValid = !ValidationErrors.Any();
    }
}

/// <summary>
/// Complete shipment creation result from image
/// </summary>
public class ImageToShipmentResult
{
    public bool IsSuccess { get; set; }
    public List<string> Errors { get; set; } = new();
    public List<string> Warnings { get; set; } = new();

    // Processing results
    public ImageProcessingResult? ImageProcessingResult { get; set; }
    public InboundShipmentResponse? ShipmentResponse { get; set; }

    // JSON processing workflow
    public List<BoxDataRow> ExtractedBoxData { get; set; } = new();
    public bool JsonExtracted { get; set; } = false;
    public bool JsonEdited { get; set; } = false;
    public bool DataProcessed { get; set; } = false;

    // Consolidated data for API calls
    public List<ConsolidatedItemDetails> ConsolidatedItems { get; set; } = new();
    public List<ConsolidatedBoxDetails> ConsolidatedBoxes { get; set; } = new();

    // Summary
    public int TotalBoxesProcessed { get; set; }
    public int ValidBoxes { get; set; }
    public int InvalidBoxes { get; set; }
    public int UniqueSkuCount { get; set; }
    public decimal TotalShipmentWeightKg { get; set; }
    public string? ShipmentId { get; set; }
    public DateTime ProcessedAt { get; set; } = DateTime.UtcNow;
    public TimeSpan TotalProcessingTime { get; set; }
}
