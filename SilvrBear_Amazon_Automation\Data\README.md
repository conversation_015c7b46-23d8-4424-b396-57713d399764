# Data Files

This folder contains the data files required for the Image to Shipment processing functionality.

## Files

### 1. Mapping.xlsx
This Excel file contains the product mapping data with the following columns:
- **Product Name**: The name of the product as it appears in images
- **SKU**: The unique SKU for the product
- **Barcode Type**: Type of barcode (old/new)
- **Weight**: Weight of the product in kg
- **Category**: Product category (optional)
- **Description**: Product description (optional)
- **Active**: Whether the product is active (true/false)

### 2. sample-image.jpg
A sample image showing the expected format for shipment data tables.

## Expected Image Format

The system expects images containing handwritten tables with the following columns:
- **Box No.**: Sequential box number
- **Product Name**: Name of the product (should match names in Mapping.xlsx)
- **Barcode Type**: Type of barcode (old/new)
- **Quantity**: Number of items in the box
- **Dimensions**: Box dimensions in format LxWxH (e.g., 58X38X26)
- **Remarks**: Additional notes, typically weight ranges (e.g., "269 To 300")

## Usage

1. Ensure Mapping.xlsx contains all your product data
2. Upload an image containing the shipment table
3. The system will:
   - Extract data using OpenAI Vision API
   - Map product names to the closest match in Mapping.xlsx
   - Calculate weights and dimensions
   - Create an Amazon inbound shipment

## Configuration

The system uses the following default settings:
- **Ship From Address**: Dipti Jadhav, Shop No. 4, Ground Floor, Sanskruti CHS, Behind St. Xavier School, Mandvi Pada Road, Kashigaon, Mira Road, Maharashtra, 401107, IN
- **Default Fulfillment Center**: ISK3 (Amazon India)
- **Booking Preference**: Earliest available slot

These can be modified in the Constants/ShipmentConstants.cs file.
