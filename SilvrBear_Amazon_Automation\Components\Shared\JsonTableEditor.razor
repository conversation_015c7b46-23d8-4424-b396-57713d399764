@using SilvrBear_Amazon_Automation.Models
@inject ILogger<JsonTableEditor> Logger

<div class="card">
    <div class="card-header">
        <div class="d-flex justify-content-between align-items-center">
            <h5 class="card-title mb-0">
                <i class="bi bi-table"></i> Edit Extracted Data
            </h5>
            <div>
                <button type="button" class="btn btn-sm btn-outline-secondary me-2" @onclick="ResetChanges" disabled="@(!HasChanges)">
                    <i class="bi bi-arrow-clockwise"></i> Reset
                </button>
                <button type="button" class="btn btn-sm btn-success" @onclick="SaveChanges" disabled="@(!HasChanges)">
                    <i class="bi bi-check-lg"></i> Save Changes
                </button>
            </div>
        </div>
    </div>
    <div class="card-body">
        @if (BoxData != null && BoxData.Any())
        {
            <div class="table-responsive">
                <table class="table table-sm table-bordered">
                    <thead class="table-light">
                        <tr>
                            <th style="width: 100px;">Box #</th>
                            <th style="width: 250px;">Product Name</th>
                            <th style="width: 140px;">Barcode Type</th>
                            <th style="width: 120px;">Quantity</th>
                            <th style="width: 180px;">Dimensions</th>
                            <th style="width: 250px;">Remarks</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var box in BoxData.Select((box, index) => new { box, index }))
                        {
                            <tr class="@GetRowClass(box.box)">
                                <td>
                                    <input type="number" class="form-control form-control-sm"
                                           @bind="box.box.BoxNumber" @oninput="HandleDataChanged"
                                           min="1" max="999" />
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm"
                                           @bind="box.box.ProductName" @oninput="HandleDataChanged"
                                           placeholder="Enter product name" />
                                </td>
                                <td>
                                    <select class="form-select form-select-sm"
                                            @bind="box.box.BarcodeType" @onchange="HandleDataChanged">
                                        <option value="">Select...</option>
                                        <option value="OLD">OLD</option>
                                        <option value="NEW">NEW</option>
                                        <option value="">None</option>
                                    </select>
                                </td>
                                <td>
                                    <input type="number" class="form-control form-control-sm"
                                           @bind="box.box.Quantity" @oninput="HandleDataChanged"
                                           min="1" max="9999" />
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm"
                                           @bind="box.box.Dimensions" @oninput="HandleDataChanged"
                                           placeholder="e.g., 58x38x26" />
                                </td>
                                <td>
                                    <input type="text" class="form-control form-control-sm"
                                           @bind="box.box.Remarks" @oninput="HandleDataChanged"
                                           placeholder="Optional remarks" />
                                </td>
                            </tr>
                            @if (box.box.ValidationErrors.Any() || box.box.ValidationWarnings.Any())
                            {
                                <tr class="table-light">
                                    <td colspan="6" class="small">
                                        @if (box.box.ValidationErrors.Any())
                                        {
                                            <div class="text-danger">
                                                <strong>Errors:</strong> @string.Join(", ", box.box.ValidationErrors)
                                            </div>
                                        }
                                        @if (box.box.ValidationWarnings.Any())
                                        {
                                            <div class="text-warning">
                                                <strong>Warnings:</strong> @string.Join(", ", box.box.ValidationWarnings)
                                            </div>
                                        }
                                    </td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>
            </div>

            <div class="mt-3">
                <div class="row">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <strong>Summary:</strong> @BoxData.Count boxes, 
                            @BoxData.Count(b => b.IsValid) valid, 
                            @BoxData.Count(b => !b.IsValid) with issues
                        </small>
                    </div>
                    <div class="col-md-6 text-end">
                        @if (HasChanges)
                        {
                            <small class="text-warning">
                                <i class="bi bi-exclamation-triangle"></i> You have unsaved changes
                            </small>
                        }
                        else
                        {
                            <small class="text-success">
                                <i class="bi bi-check-circle"></i> All changes saved
                            </small>
                        }
                    </div>
                </div>
            </div>
        }
        else
        {
            <div class="text-center py-4 text-muted">
                <i class="bi bi-table display-4"></i>
                <p class="mt-2">No data to edit</p>
            </div>
        }
    </div>
</div>

@code {
    [Parameter] public List<BoxDataRow>? BoxData { get; set; }
    [Parameter] public EventCallback<List<BoxDataRow>> OnDataSaved { get; set; }
    [Parameter] public EventCallback OnDataChanged { get; set; }

    private List<BoxDataRow>? originalData;
    private bool HasChanges => BoxData != null && originalData != null && !AreEqual(BoxData, originalData);

    protected override void OnParametersSet()
    {
        if (BoxData != null)
        {
            // Create a deep copy for comparison
            originalData = BoxData.Select(box => new BoxDataRow
            {
                BoxNumber = box.BoxNumber,
                ProductName = box.ProductName,
                BarcodeType = box.BarcodeType,
                Quantity = box.Quantity,
                Dimensions = box.Dimensions,
                Remarks = box.Remarks,
                IsValid = box.IsValid,
                ValidationErrors = new List<string>(box.ValidationErrors),
                ValidationWarnings = new List<string>(box.ValidationWarnings)
            }).ToList();

            // Validate all boxes
            ValidateAllBoxes();
        }
    }

    private void ValidateAllBoxes()
    {
        if (BoxData == null) return;

        foreach (var box in BoxData)
        {
            box.Validate();
        }
    }

    private async Task SaveChanges()
    {
        if (BoxData == null) return;

        try
        {
            // Validate all data before saving
            ValidateAllBoxes();

            // Update original data
            originalData = BoxData.Select(box => new BoxDataRow
            {
                BoxNumber = box.BoxNumber,
                ProductName = box.ProductName,
                BarcodeType = box.BarcodeType,
                Quantity = box.Quantity,
                Dimensions = box.Dimensions,
                Remarks = box.Remarks,
                IsValid = box.IsValid,
                ValidationErrors = new List<string>(box.ValidationErrors),
                ValidationWarnings = new List<string>(box.ValidationWarnings)
            }).ToList();

            await OnDataSaved.InvokeAsync(BoxData);
            Logger.LogInformation("JSON table data saved with {BoxCount} boxes", BoxData.Count);
        }
        catch (Exception ex)
        {
            Logger.LogError(ex, "Error saving JSON table data");
        }
    }

    private void ResetChanges()
    {
        if (originalData == null) return;

        BoxData = originalData.Select(box => new BoxDataRow
        {
            BoxNumber = box.BoxNumber,
            ProductName = box.ProductName,
            BarcodeType = box.BarcodeType,
            Quantity = box.Quantity,
            Dimensions = box.Dimensions,
            Remarks = box.Remarks,
            IsValid = box.IsValid,
            ValidationErrors = new List<string>(box.ValidationErrors),
            ValidationWarnings = new List<string>(box.ValidationWarnings)
        }).ToList();

        StateHasChanged();
        Logger.LogInformation("JSON table data reset to original values");
    }

    private string GetRowClass(BoxDataRow box)
    {
        if (!box.IsValid)
            return "table-warning";
        if (box.ValidationWarnings.Any())
            return "table-light";
        return "";
    }

    private async Task HandleDataChanged()
    {
        ValidateAllBoxes();
        await OnDataChanged.InvokeAsync();
        StateHasChanged();
    }

    private bool AreEqual(List<BoxDataRow> list1, List<BoxDataRow> list2)
    {
        if (list1.Count != list2.Count) return false;

        for (int i = 0; i < list1.Count; i++)
        {
            var box1 = list1[i];
            var box2 = list2[i];

            if (box1.BoxNumber != box2.BoxNumber ||
                box1.ProductName != box2.ProductName ||
                box1.BarcodeType != box2.BarcodeType ||
                box1.Quantity != box2.Quantity ||
                box1.Dimensions != box2.Dimensions ||
                box1.Remarks != box2.Remarks)
            {
                return false;
            }
        }

        return true;
    }
}
