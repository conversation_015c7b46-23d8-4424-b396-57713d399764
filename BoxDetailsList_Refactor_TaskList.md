# Task List: Box Details List Grid & Navigation Update

## 1. Remove the 'Test Mapping Debug' Tab from UI
- Edit `NavMenu.razor` to remove the navigation item for "Test Mapping Debug".

## 2. Refactor "Box Details List (for Amazon API)" Grid

### a. Remove "Box ID" Column
- Edit the Razor table in `ImageShipmentProcessor.razor` to remove the "Box ID" column from both the header and all rows.

### b. One Row per Unique Box Number
- Group all box data by `BoxNumber`.
- For each unique box number, display only one row in the grid.

### c. Weight Column: Sum of All Contents in the Box
- For each box, calculate the sum of the total weights of all contained SKUs (sum of `Quantity × UnitWeightKg` for all items in the box).
- Display this sum in the "Weight (kg)" and "Weight (lbs)" columns.

### d. Dimensions: From the First Row of Each Box Number
- For each box, display the dimensions from the first occurrence of that box number.

### e. Contents: All SKUs, Quantities, and Weights in One Row
- In the "Contents" column, list all SKUs in the box, with their quantity and unit weight, total weight, in a single row (e.g., `SKU1: 10 pcs (0.05 kg each), Total Weight: 0.5 kg, SKU2: 5 pcs (0.04 kg each), Total Weight: 0.2 kg`).
