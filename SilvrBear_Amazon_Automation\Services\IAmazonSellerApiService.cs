using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Interface for Amazon Seller API operations
/// </summary>
public interface IAmazonSellerApiService
{
    /// <summary>
    /// Creates an inbound shipment plan
    /// </summary>
    /// <param name="items">Items to include in the shipment</param>
    /// <param name="shipFromAddress">Address shipping from</param>
    /// <param name="labelPrepPreference">Label preparation preference</param>
    /// <returns>Inbound shipment plan response</returns>
    Task<AmazonApiResponse<InboundShipmentPlanResponse>> CreateInboundShipmentPlanAsync(
        List<ShipmentItem> items,
        Address shipFromAddress,
        string labelPrepPreference = "SELLER_LABEL");

    /// <summary>
    /// Creates an inbound shipment using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID from the plan creation</param>
    /// <param name="request">Shipment creation request</param>
    /// <returns>Create shipment response</returns>
    Task<AmazonApiResponse<CreateInboundShipmentResponse>> CreateInboundShipmentAsync(
        string inboundPlanId,
        CreateInboundShipmentRequest request);

    /// <summary>
    /// Updates inbound shipment packing information using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="request">Shipment update request</param>
    /// <returns>Update response</returns>
    Task<AmazonApiResponse<object>> UpdateInboundShipmentAsync(
        string inboundPlanId,
        CreateInboundShipmentRequest request);

    /// <summary>
    /// Gets inbound shipment details (preserved from v0 API)
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Shipment details</returns>
    Task<AmazonApiResponse<GetShipmentResponse>> GetInboundShipmentAsync(string shipmentId);

    /// <summary>
    /// Gets list of inbound shipments (preserved from v0 API)
    /// </summary>
    /// <param name="shipmentStatusList">Filter by status</param>
    /// <param name="shipmentIdList">Filter by shipment IDs</param>
    /// <param name="lastUpdatedAfter">Filter by last updated date</param>
    /// <param name="lastUpdatedBefore">Filter by last updated date</param>
    /// <returns>List of shipments</returns>
    Task<AmazonApiResponse<List<GetShipmentResponse>>> GetInboundShipmentsAsync(
        List<string>? shipmentStatusList = null,
        List<string>? shipmentIdList = null,
        DateTime? lastUpdatedAfter = null,
        DateTime? lastUpdatedBefore = null);

    /// <summary>
    /// Generate transportation options using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <returns>Transportation options response</returns>
    Task<AmazonApiResponse<object>> GenerateTransportationOptionsAsync(string inboundPlanId);

    /// <summary>
    /// List available transportation options using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <returns>Transportation options list</returns>
    Task<AmazonApiResponse<object>> ListTransportationOptionsAsync(string inboundPlanId);

    /// <summary>
    /// Confirm transportation options using v2024-03-20 API
    /// </summary>
    /// <param name="inboundPlanId">Inbound plan ID</param>
    /// <param name="transportationOptionId">Transportation option ID</param>
    /// <returns>Confirmation response</returns>
    Task<AmazonApiResponse<object>> ConfirmTransportationOptionsAsync(string inboundPlanId, string transportationOptionId);

    /// <summary>
    /// Gets labels for a shipment (preserved from v0 API)
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <param name="pageType">Page type (PackageLabel_Letter_6, etc.)</param>
    /// <param name="labelType">Label type (UNIQUE, PALLET)</param>
    /// <param name="numberOfPackages">Number of packages</param>
    /// <returns>Labels response</returns>
    Task<AmazonApiResponse<object>> GetLabelsAsync(
        string shipmentId,
        string pageType = "PackageLabel_Letter_6",
        string labelType = "UNIQUE",
        int? numberOfPackages = null);

    /// <summary>
    /// Gets bill of lading for LTL shipments (preserved from v0 API)
    /// </summary>
    /// <param name="shipmentId">Shipment ID</param>
    /// <returns>Bill of lading</returns>
    Task<AmazonApiResponse<object>> GetBillOfLadingAsync(string shipmentId);

    /// <summary>
    /// Validates credentials and connectivity
    /// </summary>
    /// <returns>True if valid</returns>
    Task<bool> ValidateCredentialsAsync();
}
