using System.Text.RegularExpressions;
using SilvrBear_Amazon_Automation.Constants;
using SilvrBear_Amazon_Automation.Models;
using OfficeOpenXml;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Service for managing product mapping from Excel files
/// </summary>
public class ProductMappingService : IProductMappingService
{
    private readonly ILogger<ProductMappingService> _logger;
    private readonly IWebHostEnvironment _environment;
    private ProductMappingData? _cachedMapping;
    private readonly SemaphoreSlim _loadingSemaphore = new(1, 1);

    public ProductMappingService(ILogger<ProductMappingService> logger, IWebHostEnvironment environment)
    {
        _logger = logger;
        _environment = environment;
        
        // Set EPPlus license context
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    /// <summary>
    /// Loads product mapping from Excel file
    /// </summary>
    public async Task<ProductMappingData> LoadProductMappingAsync(string filePath)
    {
        await _loadingSemaphore.WaitAsync();
        try
        {
            _logger.LogInformation("Loading product mapping from: {FilePath}", filePath);

            if (!File.Exists(filePath))
            {
                throw new FileNotFoundException(string.Format(ShipmentConstants.ErrorMessages.MappingFileNotFound, filePath));
            }

            var mappingData = new ProductMappingData
            {
                SourceFile = filePath,
                LoadedAt = DateTime.UtcNow
            };

            using var package = new ExcelPackage(new FileInfo(filePath));
            var worksheet = package.Workbook.Worksheets.FirstOrDefault();
            
            if (worksheet == null)
            {
                throw new InvalidOperationException("No worksheets found in the Excel file");
            }

            // Assume first row contains headers
            var rowCount = worksheet.Dimension?.Rows ?? 0;
            if (rowCount <= 1)
            {
                throw new InvalidOperationException("Excel file contains no data rows");
            }

            // Read headers to determine column positions
            var headers = new Dictionary<string, int>();
            for (int col = 1; col <= worksheet.Dimension.Columns; col++)
            {
                var header = worksheet.Cells[1, col].Text?.Trim().ToLower();
                if (!string.IsNullOrEmpty(header))
                {
                    headers[header] = col;
                }
            }

            // Map common header variations
            var productNameCol = GetColumnIndex(headers, "product name", "productname", "product", "name");
            var skuCol = GetColumnIndex(headers, "sku", "product sku", "item sku");
            var barcodeTypeCol = GetColumnIndex(headers, "barcode type", "barcodetype", "barcode", "type");
            var weightCol = GetColumnIndex(headers, "weight", "item weight", "weight kg", "weight(kg)");
            var categoryCol = GetColumnIndex(headers, "category", "product category", "type");
            var descriptionCol = GetColumnIndex(headers, "description", "product description", "desc");
            var activeCol = GetColumnIndex(headers, "active", "is active", "status");

            // Read data rows
            for (int row = 2; row <= rowCount; row++)
            {
                try
                {
                    var productName = GetCellValue(worksheet, row, productNameCol);
                    var sku = GetCellValue(worksheet, row, skuCol);

                    if (string.IsNullOrWhiteSpace(productName) || string.IsNullOrWhiteSpace(sku))
                    {
                        _logger.LogWarning("Skipping row {Row}: Missing product name or SKU", row);
                        continue;
                    }

                    var product = new ProductMapping
                    {
                        ProductName = productName.Trim(),
                        SKU = sku.Trim(),
                        BarcodeType = GetCellValue(worksheet, row, barcodeTypeCol)?.Trim(),
                        WeightKg = ParseWeight(GetCellValue(worksheet, row, weightCol)),
                        Category = GetCellValue(worksheet, row, categoryCol)?.Trim(),
                        Description = GetCellValue(worksheet, row, descriptionCol)?.Trim(),
                        IsActive = ParseActive(GetCellValue(worksheet, row, activeCol))
                    };

                    mappingData.Products.Add(product);

                    // Build lookup dictionaries
                    var lookupKey = CreateLookupKey(product.ProductName, product.BarcodeType);
                    mappingData.ProductLookup[lookupKey] = product;

                    if (!mappingData.SKULookup.ContainsKey(product.SKU))
                    {
                        mappingData.SKULookup[product.SKU] = new List<ProductMapping>();
                    }
                    mappingData.SKULookup[product.SKU].Add(product);
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing row {Row}: {Error}", row, ex.Message);
                }
            }

            _cachedMapping = mappingData;
            _logger.LogInformation(ShipmentConstants.SuccessMessages.MappingLoadedSuccessfully, mappingData.Products.Count);

            return mappingData;
        }
        finally
        {
            _loadingSemaphore.Release();
        }
    }

    /// <summary>
    /// Gets all available product names for OpenAI processing
    /// </summary>
    public async Task<List<string>> GetAvailableProductNamesAsync()
    {
        var mapping = await GetOrLoadMappingAsync();
        return mapping.Products
            .Where(p => p.IsActive)
            .Select(p => p.ProductName)
            .Distinct()
            .OrderBy(name => name)
            .ToList();
    }

    /// <summary>
    /// Finds the best matching product for a given name
    /// </summary>
    public async Task<ProductMapping?> FindBestMatchAsync(string productName, string? barcodeType = null)
    {
        var mapping = await GetOrLoadMappingAsync();

        // Try exact match first
        var exactMatch = await GetProductByNameAsync(productName, barcodeType);
        if (exactMatch != null)
        {
            return exactMatch;
        }

        // Try fuzzy matching
        var candidates = mapping.Products
            .Where(p => p.IsActive)
            .Where(p => barcodeType == null || p.BarcodeType == barcodeType)
            .ToList();

        var bestMatch = candidates
            .Select(p => new { Product = p, Score = CalculateSimilarity(productName, p.ProductName) })
            .Where(x => x.Score > 0.7) // Minimum similarity threshold
            .OrderByDescending(x => x.Score)
            .FirstOrDefault();

        return bestMatch?.Product;
    }

    /// <summary>
    /// Gets product mapping by exact product name
    /// </summary>
    public async Task<ProductMapping?> GetProductByNameAsync(string productName, string? barcodeType = null)
    {
        var mapping = await GetOrLoadMappingAsync();
        var lookupKey = CreateLookupKey(productName, barcodeType);
        
        return mapping.ProductLookup.TryGetValue(lookupKey, out var product) ? product : null;
    }

    /// <summary>
    /// Gets product mapping by SKU
    /// </summary>
    public async Task<ProductMapping?> GetProductBySKUAsync(string sku)
    {
        var mapping = await GetOrLoadMappingAsync();
        
        if (mapping.SKULookup.TryGetValue(sku, out var products))
        {
            return products.FirstOrDefault(p => p.IsActive) ?? products.FirstOrDefault();
        }

        return null;
    }

    /// <summary>
    /// Validates if a product exists in the mapping
    /// </summary>
    public async Task<bool> ProductExistsAsync(string productName, string? barcodeType = null)
    {
        var product = await GetProductByNameAsync(productName, barcodeType);
        return product != null && product.IsActive;
    }

    /// <summary>
    /// Gets SKU for a product and barcode type combination (exact SKU from mapping)
    /// </summary>
    public async Task<string> GetSKUAsync(string productName, string? barcodeType)
    {
        var product = await FindBestMatchAsync(productName, barcodeType);
        if (product != null)
        {
            return product.SKU; // Return exact SKU from mapping without any modifications
        }

        // Generate SKU from product name if not found in mapping
        var cleanName = Regex.Replace(productName, @"[^a-zA-Z0-9]", "").ToUpper();
        if (cleanName.Length > 10)
        {
            cleanName = cleanName.Substring(0, 10);
        }

        var barcodePrefix = barcodeType?.ToUpper() switch
        {
            "OLD" => "O",
            "NEW" => "N",
            _ => "X"
        };

        return $"GEN-{cleanName}-{barcodePrefix}";
    }

    /// <summary>
    /// Generates SKU for a product and barcode type combination (for backward compatibility)
    /// </summary>
    [Obsolete("Use GetSKUAsync instead for exact SKU from mapping")]
    public async Task<string> GenerateSKUAsync(string productName, string? barcodeType, int boxNumber)
    {
        return await GetSKUAsync(productName, barcodeType);
    }

    /// <summary>
    /// Gets product weight by name and barcode type
    /// </summary>
    public async Task<decimal> GetProductWeightAsync(string productName, string? barcodeType)
    {
        var product = await FindBestMatchAsync(productName, barcodeType);
        return product?.WeightKg ?? ShipmentConstants.Conversions.DefaultItemWeightGrams / 1000m;
    }

    /// <summary>
    /// Refreshes the product mapping cache
    /// </summary>
    public async Task<ProductMappingData> RefreshMappingAsync()
    {
        var mappingFilePath = Path.Combine(_environment.ContentRootPath, "Data", ShipmentConstants.FileProcessing.MappingFileName);
        _cachedMapping = null;
        return await LoadProductMappingAsync(mappingFilePath);
    }

    /// <summary>
    /// Gets mapping statistics
    /// </summary>
    public async Task<ProductMappingStatistics> GetMappingStatisticsAsync()
    {
        var mapping = await GetOrLoadMappingAsync();
        
        var stats = new ProductMappingStatistics
        {
            TotalProducts = mapping.Products.Count,
            ActiveProducts = mapping.Products.Count(p => p.IsActive),
            InactiveProducts = mapping.Products.Count(p => !p.IsActive),
            ProductsWithOldBarcode = mapping.Products.Count(p => p.BarcodeType?.ToLower() == "old"),
            ProductsWithNewBarcode = mapping.Products.Count(p => p.BarcodeType?.ToLower() == "new"),
            ProductsWithoutBarcode = mapping.Products.Count(p => string.IsNullOrEmpty(p.BarcodeType)),
            LastUpdated = mapping.LoadedAt,
            SourceFile = mapping.SourceFile,
            CategoryCounts = mapping.Products
                .Where(p => !string.IsNullOrEmpty(p.Category))
                .GroupBy(p => p.Category!)
                .ToDictionary(g => g.Key, g => g.Count())
        };

        stats.UniqueCategories = stats.CategoryCounts.Count;
        return stats;
    }

    #region Private Helper Methods

    private async Task<ProductMappingData> GetOrLoadMappingAsync()
    {
        if (_cachedMapping == null)
        {
            var mappingFilePath = Path.Combine(_environment.ContentRootPath, "Data", ShipmentConstants.FileProcessing.MappingFileName);
            await LoadProductMappingAsync(mappingFilePath);
        }

        return _cachedMapping!;
    }

    private static int GetColumnIndex(Dictionary<string, int> headers, params string[] possibleNames)
    {
        foreach (var name in possibleNames)
        {
            if (headers.TryGetValue(name, out var index))
            {
                return index;
            }
        }
        return -1;
    }

    private static string? GetCellValue(ExcelWorksheet worksheet, int row, int col)
    {
        if (col <= 0) return null;
        return worksheet.Cells[row, col].Text?.Trim();
    }

    private static decimal ParseWeight(string? weightText)
    {
        if (string.IsNullOrWhiteSpace(weightText))
        {
            return ShipmentConstants.Conversions.DefaultItemWeightGrams / 1000m;
        }

        if (decimal.TryParse(weightText.Replace("kg", "").Replace("g", "").Trim(), out var weight))
        {
            // If weight is >= 1, assume it's in grams and convert to kg
            // If weight is < 1, assume it's already in kg
            return weight >= 1 ? weight / 1000m : weight;
        }

        return ShipmentConstants.Conversions.DefaultItemWeightGrams / 1000m;
    }

    private static bool ParseActive(string? activeText)
    {
        if (string.IsNullOrWhiteSpace(activeText))
        {
            return true; // Default to active
        }

        return activeText.ToLower() switch
        {
            "true" or "yes" or "y" or "1" or "active" => true,
            "false" or "no" or "n" or "0" or "inactive" => false,
            _ => true
        };
    }

    private static string CreateLookupKey(string productName, string? barcodeType)
    {
        return $"{productName.ToLower()}|{barcodeType?.ToLower() ?? ""}";
    }

    private static double CalculateSimilarity(string text1, string text2)
    {
        if (string.IsNullOrEmpty(text1) || string.IsNullOrEmpty(text2))
        {
            return 0;
        }

        text1 = text1.ToLower().Trim();
        text2 = text2.ToLower().Trim();

        if (text1 == text2)
        {
            return 1.0;
        }

        // Simple Levenshtein distance-based similarity
        var distance = LevenshteinDistance(text1, text2);
        var maxLength = Math.Max(text1.Length, text2.Length);
        
        return 1.0 - (double)distance / maxLength;
    }

    private static int LevenshteinDistance(string s1, string s2)
    {
        var matrix = new int[s1.Length + 1, s2.Length + 1];

        for (int i = 0; i <= s1.Length; i++)
            matrix[i, 0] = i;

        for (int j = 0; j <= s2.Length; j++)
            matrix[0, j] = j;

        for (int i = 1; i <= s1.Length; i++)
        {
            for (int j = 1; j <= s2.Length; j++)
            {
                var cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
                matrix[i, j] = Math.Min(
                    Math.Min(matrix[i - 1, j] + 1, matrix[i, j - 1] + 1),
                    matrix[i - 1, j - 1] + cost);
            }
        }

        return matrix[s1.Length, s2.Length];
    }

    #endregion
}
