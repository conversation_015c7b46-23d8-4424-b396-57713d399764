using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Interface for complete image to shipment processing
/// </summary>
public interface IImageToShipmentService
{
    /// <summary>
    /// Processes an image and creates a complete inbound shipment
    /// </summary>
    /// <param name="request">Image to shipment request</param>
    /// <returns>Complete processing result</returns>
    Task<ImageToShipmentResult> ProcessImageAndCreateShipmentAsync(ImageToShipmentRequest request);

    /// <summary>
    /// Processes an image and validates data without creating shipment
    /// </summary>
    /// <param name="imageFile">Image file to process</param>
    /// <param name="shipmentName">Optional shipment name</param>
    /// <param name="fulfillmentCenterId">Optional fulfillment center ID</param>
    /// <returns>Validation result</returns>
    Task<ImageToShipmentResult> ValidateImageDataAsync(
        IFormFile imageFile, 
        string? shipmentName = null, 
        string? fulfillmentCenterId = null);

    /// <summary>
    /// Processes extracted box data and maps to products
    /// </summary>
    /// <param name="processedBoxes">Processed box data from image</param>
    /// <returns>Mapped and enriched box data</returns>
    Task<List<ProcessedBoxData>> MapAndEnrichBoxDataAsync(List<ProcessedBoxData> processedBoxes);

    /// <summary>
    /// Converts processed box data to shipment request
    /// </summary>
    /// <param name="processedBoxes">Processed and mapped box data</param>
    /// <param name="shipmentName">Shipment name</param>
    /// <param name="fulfillmentCenterId">Fulfillment center ID</param>
    /// <returns>Create inbound shipment request</returns>
    Task<CreateInboundShipmentRequest> ConvertToShipmentRequestAsync(
        List<ProcessedBoxData> processedBoxes,
        string shipmentName,
        string fulfillmentCenterId);

    /// <summary>
    /// Calculates box weights and dimensions
    /// </summary>
    /// <param name="processedBox">Box data to calculate</param>
    /// <returns>Updated box data with calculations</returns>
    Task<ProcessedBoxData> CalculateBoxMetricsAsync(ProcessedBoxData processedBox);

    /// <summary>
    /// Validates complete shipment data before creation
    /// </summary>
    /// <param name="shipmentRequest">Shipment request to validate</param>
    /// <returns>Validation result</returns>
    Task<(bool IsValid, List<string> Errors, List<string> Warnings)> ValidateShipmentDataAsync(
        CreateInboundShipmentRequest shipmentRequest);

    /// <summary>
    /// Gets processing statistics
    /// </summary>
    /// <returns>Processing statistics</returns>
    Task<ImageProcessingStatistics> GetProcessingStatisticsAsync();
}

/// <summary>
/// Image processing statistics
/// </summary>
public class ImageProcessingStatistics
{
    public int TotalImagesProcessed { get; set; }
    public int SuccessfulProcessing { get; set; }
    public int FailedProcessing { get; set; }
    public int TotalBoxesExtracted { get; set; }
    public int TotalShipmentsCreated { get; set; }
    public TimeSpan AverageProcessingTime { get; set; }
    public DateTime LastProcessedAt { get; set; }
    public Dictionary<string, int> ErrorCounts { get; set; } = new();
    public Dictionary<string, int> ProductCounts { get; set; } = new();
}
