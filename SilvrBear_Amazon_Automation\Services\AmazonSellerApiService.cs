using Microsoft.Extensions.Options;
using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Implementation of Amazon Seller API service
/// </summary>
public class AmazonSellerApiService : IAmazonSellerApiService
{
    private readonly AmazonApiClient _apiClient;
    private readonly AmazonCredentials _credentials;
    private readonly ILogger<AmazonSellerApiService> _logger;

    public AmazonSellerApiService(
        AmazonApiClient apiClient,
        IOptions<AmazonCredentials> credentials,
        ILogger<AmazonSellerApiService> logger)
    {
        _apiClient = apiClient;
        _credentials = credentials.Value;
        _logger = logger;
    }

    public async Task<AmazonApiResponse<InboundShipmentPlanResponse>> CreateInboundShipmentPlanAsync(
        List<ShipmentItem> items,
        Address shipFromAddress,
        string labelPrepPreference = "SELLER_LABEL")
    {
        try
        {
            var endpoint = "/fba/inbound/v0/plans";
            
            var requestPayload = new
            {
                ShipFromAddress = shipFromAddress,
                LabelPrepPreference = labelPrepPreference,
                ShipToCountryCode = "IN", // For India marketplace
                InboundShipmentPlanRequestItems = items.Select(item => new
                {
                    SellerSKU = item.Sku,
                    ASIN = item.Asin,
                    Condition = item.Condition ?? "NewItem",
                    Quantity = item.Quantity,
                    QuantityInCase = 1,
                    PrepDetailsList = new object[] { }
                }).ToList()
            };

            _logger.LogInformation("Creating inbound shipment plan for {ItemCount} items", items.Count);

            return await _apiClient.PostAsync<InboundShipmentPlanResponse>(endpoint, requestPayload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inbound shipment plan");
            throw;
        }
    }

    public async Task<AmazonApiResponse<CreateInboundShipmentResponse>> CreateInboundShipmentAsync(
        string shipmentId,
        CreateInboundShipmentRequest request)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}";
            
            var requestPayload = new
            {
                ShipmentName = request.ShipmentName,
                DestinationFulfillmentCenterId = request.DestinationFulfillmentCenterId,
                LabelPrepPreference = request.LabelPrepPreference,
                AreCasesRequired = false,
                InboundShipmentItems = request.Items.Select(item => new
                {
                    SellerSKU = item.Sku,
                    QuantityShipped = item.Quantity,
                    QuantityInCase = 1,
                    ReleaseDate = DateTime.UtcNow.ToString("yyyy-MM-dd"),
                    PrepDetailsList = new object[] { }
                }).ToList()
            };

            _logger.LogInformation("Creating inbound shipment {ShipmentId} with name {ShipmentName}", 
                shipmentId, request.ShipmentName);

            return await _apiClient.PostAsync<CreateInboundShipmentResponse>(endpoint, requestPayload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating inbound shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<object>> UpdateInboundShipmentAsync(
        string shipmentId,
        CreateInboundShipmentRequest request)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}";
            
            var requestPayload = new
            {
                ShipmentName = request.ShipmentName,
                DestinationFulfillmentCenterId = request.DestinationFulfillmentCenterId,
                LabelPrepPreference = request.LabelPrepPreference,
                AreCasesRequired = false,
                InboundShipmentItems = request.Items.Select(item => new
                {
                    SellerSKU = item.Sku,
                    QuantityShipped = item.Quantity,
                    QuantityInCase = 1,
                    PrepDetailsList = new object[] { }
                }).ToList()
            };

            _logger.LogInformation("Updating inbound shipment {ShipmentId}", shipmentId);

            return await _apiClient.PutAsync<object>(endpoint, requestPayload);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error updating inbound shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<GetShipmentResponse>> GetInboundShipmentAsync(string shipmentId)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}";
            
            _logger.LogInformation("Getting inbound shipment details for {ShipmentId}", shipmentId);

            return await _apiClient.GetAsync<GetShipmentResponse>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inbound shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<List<GetShipmentResponse>>> GetInboundShipmentsAsync(
        List<string>? shipmentStatusList = null,
        List<string>? shipmentIdList = null,
        DateTime? lastUpdatedAfter = null,
        DateTime? lastUpdatedBefore = null)
    {
        try
        {
            var endpoint = "/fba/inbound/v0/shipments";
            var queryParams = new Dictionary<string, string>
            {
                ["MarketplaceId"] = _credentials.MarketplaceId
            };

            if (shipmentStatusList?.Any() == true)
                queryParams["ShipmentStatusList"] = string.Join(",", shipmentStatusList);

            if (shipmentIdList?.Any() == true)
                queryParams["ShipmentIdList"] = string.Join(",", shipmentIdList);

            if (lastUpdatedAfter.HasValue)
                queryParams["LastUpdatedAfter"] = lastUpdatedAfter.Value.ToString("yyyy-MM-ddTHH:mm:ssZ");

            if (lastUpdatedBefore.HasValue)
                queryParams["LastUpdatedBefore"] = lastUpdatedBefore.Value.ToString("yyyy-MM-ddTHH:mm:ssZ");

            _logger.LogInformation("Getting inbound shipments list");

            return await _apiClient.GetAsync<List<GetShipmentResponse>>(endpoint, queryParams);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting inbound shipments list");
            throw;
        }
    }

    public async Task<AmazonApiResponse<object>> PutTransportContentAsync(string shipmentId, object transportDetails)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}/transport";
            
            _logger.LogInformation("Putting transport content for shipment {ShipmentId}", shipmentId);

            return await _apiClient.PutAsync<object>(endpoint, transportDetails);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error putting transport content for shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<object>> EstimateTransportRequestAsync(string shipmentId)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}/transport/estimate";
            
            _logger.LogInformation("Estimating transport for shipment {ShipmentId}", shipmentId);

            return await _apiClient.PostAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error estimating transport for shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<object>> ConfirmTransportRequestAsync(string shipmentId)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}/transport/confirm";
            
            _logger.LogInformation("Confirming transport for shipment {ShipmentId}", shipmentId);

            return await _apiClient.PostAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error confirming transport for shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<object>> GetLabelsAsync(
        string shipmentId,
        string pageType = "PackageLabel_Letter_6",
        string labelType = "UNIQUE",
        int? numberOfPackages = null)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}/labels";
            var queryParams = new Dictionary<string, string>
            {
                ["PageType"] = pageType,
                ["LabelType"] = labelType
            };

            if (numberOfPackages.HasValue)
                queryParams["NumberOfPackages"] = numberOfPackages.Value.ToString();

            _logger.LogInformation("Getting labels for shipment {ShipmentId}", shipmentId);

            return await _apiClient.GetAsync<object>(endpoint, queryParams);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting labels for shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<AmazonApiResponse<object>> GetBillOfLadingAsync(string shipmentId)
    {
        try
        {
            var endpoint = $"/fba/inbound/v0/shipments/{shipmentId}/billOfLading";
            
            _logger.LogInformation("Getting bill of lading for shipment {ShipmentId}", shipmentId);

            return await _apiClient.GetAsync<object>(endpoint);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting bill of lading for shipment {ShipmentId}", shipmentId);
            throw;
        }
    }

    public async Task<bool> ValidateCredentialsAsync()
    {
        try
        {
            // Try to get a simple endpoint to validate credentials
            var endpoint = "/fba/inbound/v0/shipments";
            var queryParams = new Dictionary<string, string>
            {
                ["MarketplaceId"] = _credentials.MarketplaceId
            };

            var response = await _apiClient.GetAsync<List<GetShipmentResponse>>(endpoint, queryParams);
            
            var isValid = response.IsSuccess || 
                         (response.Errors.Any() && !response.Errors.Any(e => 
                             e.Code.Contains("Unauthorized") || 
                             e.Code.Contains("InvalidAccessToken") ||
                             e.Code.Contains("AccessDenied")));

            _logger.LogInformation("Credential validation result: {IsValid}", isValid);
            
            return isValid;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating credentials");
            return false;
        }
    }
}
