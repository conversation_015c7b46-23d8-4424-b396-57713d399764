using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Interface for product mapping operations
/// </summary>
public interface IProductMappingService
{
    /// <summary>
    /// Loads product mapping from Excel file
    /// </summary>
    /// <param name="filePath">Path to the Excel mapping file</param>
    /// <returns>Product mapping data</returns>
    Task<ProductMappingData> LoadProductMappingAsync(string filePath);

    /// <summary>
    /// Gets all available product names for OpenAI processing
    /// </summary>
    /// <returns>List of product names</returns>
    Task<List<string>> GetAvailableProductNamesAsync();

    /// <summary>
    /// Finds the best matching product for a given name
    /// </summary>
    /// <param name="productName">Product name to match</param>
    /// <param name="barcodeType">Optional barcode type filter</param>
    /// <returns>Best matching product or null if not found</returns>
    Task<ProductMapping?> FindBestMatchAsync(string productName, string? barcodeType = null);

    /// <summary>
    /// Gets product mapping by exact product name
    /// </summary>
    /// <param name="productName">Exact product name</param>
    /// <param name="barcodeType">Optional barcode type filter</param>
    /// <returns>Product mapping or null if not found</returns>
    Task<ProductMapping?> GetProductByNameAsync(string productName, string? barcodeType = null);

    /// <summary>
    /// Gets product mapping by SKU
    /// </summary>
    /// <param name="sku">Product SKU</param>
    /// <returns>Product mapping or null if not found</returns>
    Task<ProductMapping?> GetProductBySKUAsync(string sku);

    /// <summary>
    /// Validates if a product exists in the mapping
    /// </summary>
    /// <param name="productName">Product name to validate</param>
    /// <param name="barcodeType">Optional barcode type filter</param>
    /// <returns>True if product exists</returns>
    Task<bool> ProductExistsAsync(string productName, string? barcodeType = null);

    /// <summary>
    /// Gets exact SKU for a product and barcode type combination from mapping
    /// </summary>
    /// <param name="productName">Product name</param>
    /// <param name="barcodeType">Barcode type</param>
    /// <returns>Exact SKU from mapping</returns>
    Task<string> GetSKUAsync(string productName, string? barcodeType);

    /// <summary>
    /// Generates SKU for a product and barcode type combination (for backward compatibility)
    /// </summary>
    /// <param name="productName">Product name</param>
    /// <param name="barcodeType">Barcode type</param>
    /// <param name="boxNumber">Box number for uniqueness</param>
    /// <returns>Generated SKU</returns>
    [Obsolete("Use GetSKUAsync instead for exact SKU from mapping")]
    Task<string> GenerateSKUAsync(string productName, string? barcodeType, int boxNumber);

    /// <summary>
    /// Gets product weight by name and barcode type
    /// </summary>
    /// <param name="productName">Product name</param>
    /// <param name="barcodeType">Barcode type</param>
    /// <returns>Product weight in kg</returns>
    Task<decimal> GetProductWeightAsync(string productName, string? barcodeType);

    /// <summary>
    /// Refreshes the product mapping cache
    /// </summary>
    /// <returns>Updated product mapping data</returns>
    Task<ProductMappingData> RefreshMappingAsync();

    /// <summary>
    /// Gets mapping statistics
    /// </summary>
    /// <returns>Mapping statistics</returns>
    Task<ProductMappingStatistics> GetMappingStatisticsAsync();

}

/// <summary>
/// Product mapping statistics
/// </summary>
public class ProductMappingStatistics
{
    public int TotalProducts { get; set; }
    public int ActiveProducts { get; set; }
    public int InactiveProducts { get; set; }
    public int UniqueCategories { get; set; }
    public int ProductsWithOldBarcode { get; set; }
    public int ProductsWithNewBarcode { get; set; }
    public int ProductsWithoutBarcode { get; set; }
    public DateTime LastUpdated { get; set; }
    public string SourceFile { get; set; } = string.Empty;
    public Dictionary<string, int> CategoryCounts { get; set; } = new();
}
