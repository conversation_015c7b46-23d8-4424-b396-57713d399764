# Image Processing Workflow Refactor - Task List

## Overview
Refactor the application to focus on Image-to-Shipment processing with manual JSON editing capability and simplified workflow.

---

## 🎯 **PHASE 1: UI/UX Restructuring**

### Task 1.1: Move 'Fetch Recent Shipments' to New Tab
- [ ] **Create new Razor component**: `Components/Pages/RecentShipments.razor`
- [ ] **Copy recent shipments functionality** from `InboundShipment.razor`
- [ ] **Add new navigation item** in `NavMenu.razor`:
  ```razor
  <div class="nav-item px-3">
      <NavLink class="nav-link" href="recent-shipments">
          <span class="bi bi-list-ul" aria-hidden="true"></span> Recent Shipments
      </NavLink>
  </div>
  ```
- [ ] **Update route**: `@page "/recent-shipments"`
- [ ] **Remove recent shipments section** from `InboundShipment.razor`

### Task 1.2: Remove Inbound Shipment Tab
- [ ] **Remove navigation item** from `NavMenu.razor`:
  - Delete "Inbound Shipment" nav link
- [ ] **Keep InboundShipment.razor** for Amazon API integration but remove from navigation
- [ ] **Update main navigation** to focus on Image Processing

### Task 1.3: Clean Up Image Processing UI
- [ ] **Remove buttons** from `ImageShipmentProcessor.razor`:
  - "View Sample" button and associated `@onclick="ViewSample"`
  - "Refresh Mapping" button and associated `@onclick="RefreshMapping"`
  - "Statistics" button and associated `@onclick="ShowStatistics"`
- [ ] **Remove associated UI sections**:
  - Sample data display modal/section
  - Statistics display modal/section
  - Mapping refresh status messages

---

## 🗑️ **PHASE 2: Backend Cleanup**

### Task 2.1: Remove Unused Services & Methods
- [ ] **Remove SampleDataGenerator** (if not required):
  - Delete `Services/SampleDataGenerator.cs`
  - Remove from `Program.cs` dependency injection
  - Remove from any constructor injections
- [ ] **Clean up ImageShipmentProcessor methods**:
  - Remove `ViewSample()` method
  - Remove `RefreshMapping()` method
  - Remove `ShowStatistics()` method
- [ ] **Remove unused models**:
  - Any sample data related models
  - Statistics display models (if dedicated ones exist)

### Task 2.2: Remove Unused API Endpoints
- [ ] **Review and remove** any backend controllers/endpoints for:
  - Sample data generation
  - Statistics reporting
  - Manual mapping refresh (if separate from main mapping load)

---

## 🔄 **PHASE 3: JSON Processing Workflow**

> **IMPORTANT**: This phase involves REFACTORING existing code flow, not writing new processing logic. The data processing functionality (mapping, enrichment, box data generation) already exists in the current ValidateImage workflow - it just needs to be moved to the ProcessDataOnly step.

### Task 3.1: Modify Image Validation Step
- [ ] **REFACTOR EXISTING ValidateImage() method** in `ImageShipmentProcessor.razor`:
  - **Current Behavior**: Validates image AND processes data (enrichment, mapping, etc.)
  - **New Behavior**: Only validate image and extract JSON from OpenAI/mock data
  - **What to REMOVE from ValidateImage()**:
    - Product mapping and enrichment logic
    - Extracted Box Data generation
    - Item Details List generation
    - Box Details List generation
    - Amazon API preparation
  - **What to KEEP in ValidateImage()**:
    - Image file validation
    - OpenAI API call or mock data loading
    - Raw JSON extraction and parsing
    - Display JSON for manual editing

- [ ] **Updated ValidateImage() method structure**:
  ```csharp
  private async Task ValidateImage()
  {
      // Only get JSON from OpenAI or mock data
      // Do NOT generate Extracted Box Data, Item Details, Box Details
      
      if (_useMockData)
      {
          // Load from data.json
          rawJsonOutput = await LoadMockJsonData();
      }
      else
      {
          // Call OpenAI API (existing logic)
          var openAiResult = await OpenAIImageService.ProcessImageAsync(imageFile);
          rawJsonOutput = openAiResult.RawJsonResponse;
      }
      
      // Parse and display for manual editing
      await ParseJsonForEditing();
      
      // Enable manual editing UI
      showJsonEditor = true;
      showProcessDataButton = false; // Disabled until JSON is saved
      
      // REMOVE: All data processing logic (move to ProcessDataOnly method)
  }
  ```

- [ ] **IMPORTANT**: Move existing data processing logic to Task 4.3 (ProcessDataOnly method)

### Task 3.2: Create JSON Editor Component
- [ ] **Create new component**: `Components/Shared/JsonTableEditor.razor`
- [ ] **Features to implement**:
  - Display JSON data in tabular format
  - Editable cells for manual corrections
  - Validation for required fields
  - Save edited data back to JSON format
- [ ] **Properties needed**:
  ```csharp
  [Parameter] public List<BoxDataRow> BoxData { get; set; } = new();
  [Parameter] public EventCallback<List<BoxDataRow>> OnDataSaved { get; set; }
  ```

### Task 3.3: Add Manual JSON Editing UI
- [ ] **Add to ImageShipmentProcessor.razor**:
  ```razor
  @if (showJsonEditor)
  {
      <div class="card mt-4">
          <div class="card-header">
              <h5>Edit Extracted Data</h5>
          </div>
          <div class="card-body">
              <JsonTableEditor BoxData="editableBoxData" OnDataSaved="OnJsonDataSaved" />
              <div class="mt-3">
                  <button class="btn btn-success" @onclick="SaveJsonEdits">
                      <i class="bi bi-check-circle"></i> Save Edits
                  </button>
                  <button class="btn btn-secondary ms-2" @onclick="CancelJsonEdits">
                      Cancel
                  </button>
              </div>
          </div>
      </div>
  }
  ```

---

## ⚙️ **PHASE 4: Data Processing Logic** ✅ **COMPLETED**

### Task 4.1: Implement 'Process Data Only' Button ✅ **COMPLETED**
- [x] **Add button** to `ImageShipmentProcessor.razor`:
  ```razor
  @if (showProcessDataButton)
  {
      <button class="btn btn-primary" @onclick="ProcessDataOnly" disabled="@isProcessing">
          @if (isProcessing)
          {
              <span class="spinner-border spinner-border-sm me-2"></span>
              <text>Processing...</text>
          }
          else
          {
              <i class="bi bi-gear"></i>
              <text>Process Data Only</text>
          }
      </button>
  }
  ```

### Task 4.2: Enhanced Mapping Logic ✅ **COMPLETED**
- [x] **Update ProductMappingService** with enhanced barcode type handling:
  ```csharp
  public async Task<ProductMapping?> FindProductMappingAsync(string productName, string? barcodeType = null)
  {
      // First try exact match with barcode type
      var exactMatch = await FindExactMatch(productName, barcodeType);
      if (exactMatch != null) return exactMatch;
      
      // If barcode type is 'old' or 'new', try without barcode type
      if (!string.IsNullOrEmpty(barcodeType) && (barcodeType.ToLower() == "old" || barcodeType.ToLower() == "new"))
      {
          var productOnlyMatches = await FindProductOnlyMatches(productName);
          if (productOnlyMatches.Count == 1)
          {
              _logger.LogInformation("Found single product match for {ProductName}, ignoring barcode type {BarcodeType}", 
                  productName, barcodeType);
              return productOnlyMatches.First();
          }
      }
      
      return null;
  }
  ```

### Task 4.3: Implement ProcessDataOnly Method ✅ **COMPLETED**
- [x] **REUSE EXISTING LOGIC** - Don't create new methods, refactor existing workflow:
  - **Current State**: Data processing (enrichment, box data generation, etc.) happens directly after "Validate Image" button
  - **Required Change**: Move this existing processing logic to "Process Data Only" button
  - **Implementation Approach**:
    1. **Extract existing data processing code** from `ValidateImage()` method
    2. **Move the processing logic** to new `ProcessDataOnly()` method
    3. **Update ValidateImage()** to only handle image validation and JSON extraction
    4. **Reuse existing methods** like:
       - `ImageToShipmentService.ProcessImageAndCreateShipmentAsync()` (but stop before shipment creation)
       - Product mapping and enrichment logic
       - Consolidated data generation (Item Details List, Box Details List)
       - Box data extraction and validation

- [ ] **Create ProcessDataOnly method** in `ImageShipmentProcessor.razor`:
  ```csharp
  private async Task ProcessDataOnly()
  {
      isProcessing = true;
      
      try
      {
          // REUSE EXISTING WORKFLOW - Move existing logic from ValidateImage() here
          // Use the same ImageToShipmentService processing but with edited JSON data
          // This should handle:
          // - Product mapping and enrichment
          // - Consolidated item details generation  
          // - Consolidated box details generation
          // - Data validation and error handling
          
          // Enable create shipment button after successful processing
          showCreateShipmentButton = true;
          
          SetStatusMessage("Data processed successfully!", true);
      }
      catch (Exception ex)
      {
          SetStatusMessage($"Error processing data: {ex.Message}", false);
      }
      finally
      {
          isProcessing = false;
      }
  }
  ```

- [ ] **IMPORTANT**: Do not write new data processing methods - refactor and reuse existing code flow

---

## 🚀 **PHASE 5: Shipment Creation Integration**

### Task 5.1: Enable Create Shipment Button
- [ ] **Add conditional button** in `ImageShipmentProcessor.razor`:
  ```razor
  @if (showCreateShipmentButton)
  {
      <div class="card mt-4">
          <div class="card-header">
              <h5>Create Amazon Shipment</h5>
          </div>
          <div class="card-body">
              <button class="btn btn-success btn-lg" @onclick="CreateAmazonShipment" disabled="@isCreatingShipment">
                  @if (isCreatingShipment)
                  {
                      <span class="spinner-border spinner-border-sm me-2"></span>
                      <text>Creating Shipment...</text>
                  }
                  else
                  {
                      <i class="bi bi-box-seam"></i>
                      <text>Create Amazon Inbound Shipment</text>
                  }
              </button>
          </div>
      </div>
  }
  ```

### Task 5.2: Implement Amazon Shipment Creation
- [ ] **Create method** that uses existing `InboundShipmentService`:
  ```csharp
  private async Task CreateAmazonShipment()
  {
      isCreatingShipment = true;
      
      try
      {
          // Convert processed data to Amazon API format
          var shipmentRequest = ConvertToAmazonShipmentRequest();
          
          // Use existing InboundShipmentService
          var result = await InboundShipmentService.CreateCompleteInboundShipmentAsync(shipmentRequest);
          
          if (result.Errors.Any())
          {
              SetStatusMessage($"Shipment creation failed: {string.Join(", ", result.Errors)}", false);
          }
          else
          {
              SetStatusMessage($"Shipment created successfully! ID: {result.ShipmentId}", true);
              // Reset for next workflow
              await ResetWorkflow();
          }
      }
      catch (Exception ex)
      {
          SetStatusMessage($"Error creating shipment: {ex.Message}", false);
      }
      finally
      {
          isCreatingShipment = false;
      }
  }
  ```

---

## 📋 **PHASE 6: Data Models & State Management**

### Task 6.1: Add New State Variables
- [ ] **Add to ImageShipmentProcessor.razor** `@code` section:
  ```csharp
  // JSON Processing State
  private string rawJsonOutput = string.Empty;
  private List<BoxDataRow> editableBoxData = new();
  private List<BoxDataRow> editedJsonData = new();
  private bool showJsonEditor = false;
  
  // Processing State
  private bool showProcessDataButton = false;
  private bool isProcessing = false;
  private bool showCreateShipmentButton = false;
  private bool isCreatingShipment = false;
  
  // Processed Data
  private List<ProcessedBoxData> extractedBoxData = new();
  private List<ConsolidatedItemDetails> itemDetailsList = new();
  private List<ConsolidatedBoxDetails> boxDetailsList = new();
  ```

### Task 6.2: Create BoxDataRow Model
- [ ] **Create new model** in `Models/ImageProcessingModels.cs`:
  ```csharp
  public class BoxDataRow
  {
      public string BoxNumber { get; set; } = string.Empty;
      public string ProductName { get; set; } = string.Empty;
      public string BarcodeType { get; set; } = string.Empty;
      public int Quantity { get; set; }
      public string Dimensions { get; set; } = string.Empty;
      public string Remarks { get; set; } = string.Empty;
      public bool IsValid { get; set; } = true;
      public List<string> ValidationErrors { get; set; } = new();
  }
  ```

---

## 🧪 **PHASE 7: Testing & Validation**

### Task 7.1: Unit Tests
- [ ] **Test ProductMappingService** enhanced barcode logic
- [ ] **Test JSON parsing and editing** functionality
- [ ] **Test data enrichment** workflow
- [ ] **Test Amazon API integration** with processed data

### Task 7.2: Integration Testing
- [ ] **Test complete workflow**:
  1. Upload image
  2. Validate and get JSON
  3. Edit JSON manually
  4. Process data with mapping
  5. Create Amazon shipment
- [ ] **Test error handling** at each step
- [ ] **Test UI state management** throughout workflow

---

## 📁 **PHASE 8: File Structure Updates**

### Task 8.1: Clean Up Files
- [ ] **Remove or rename**:
  - `SampleDataGenerator.cs` (if not needed)
  - Any sample data related files
  - Statistics related components (if dedicated ones exist)

### Task 8.2: Add New Files
- [ ] **Create**:
  - `Components/Pages/RecentShipments.razor`
  - `Components/Shared/JsonTableEditor.razor`
  - Updated models in `ImageProcessingModels.cs`

---

## 🔧 **PHASE 9: Configuration & Settings**

### Task 9.1: Update Constants
- [ ] **Review ShipmentConstants.cs** for any needed updates
- [ ] **Add new constants** for workflow states and validation

### Task 9.2: Update appsettings.json
- [ ] **Remove any settings** related to removed features
- [ ] **Add new settings** if needed for JSON processing

---

## ✅ **PHASE 10: Documentation & Cleanup**

### Task 10.1: Update Documentation
- [ ] **Update README.md** with new workflow
- [ ] **Update GitHub Copilot instructions** if they exist
- [ ] **Document new API endpoints** or method signatures

### Task 10.2: Code Review Checklist
- [ ] **Remove all unused code** and imports
- [ ] **Verify error handling** is comprehensive
- [ ] **Check logging** is appropriate
- [ ] **Validate UI/UX** is intuitive
- [ ] **Test production deployment**

---

## 🎯 **Success Criteria**

1. ✅ **Simplified Navigation**: Only Image Processing and Recent Shipments tabs
2. ✅ **Manual JSON Editing**: Users can edit OpenAI extracted data before processing
3. ✅ **Enhanced Mapping**: Smart barcode type handling with fallback logic
4. ✅ **Streamlined Workflow**: Clear step-by-step process from image to shipment
5. ✅ **Amazon Integration**: Seamless shipment creation after data processing
6. ✅ **Error Handling**: Comprehensive error messages and validation
7. ✅ **Clean Codebase**: Removed unused features and optimized structure

---

## 📝 **Notes**

- **Priority Order**: Complete phases in sequence to maintain functionality
- **Testing**: Test each phase thoroughly before proceeding
- **Backup**: Ensure version control before major changes
- **User Experience**: Focus on intuitive workflow and clear visual feedback
- **Performance**: Monitor application performance during JSON editing operations

---

*Last Updated: June 17, 2025*
