using System.Diagnostics;
using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using SilvrBear_Amazon_Automation.Constants;
using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Service for processing images using OpenAI Vision API
/// </summary>
public class OpenAIImageService : IOpenAIImageService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<OpenAIImageService> _logger;
    private readonly IConfiguration _configuration;
    private readonly IWebHostEnvironment _environment;
    private readonly string _openAIApiKey;
    private readonly OpenAIUsageStatistics _usageStats = new();
    private bool _useMockData = true; // Toggle this to switch between API and mock data
    private readonly string _mockDataPath;

    public OpenAIImageService(
        HttpClient httpClient,
        ILogger<OpenAIImageService> logger,
        IConfiguration configuration,
        IWebHostEnvironment environment)
    {
        _httpClient = httpClient;
        _logger = logger;
        _configuration = configuration;
        _environment = environment;
        
        _openAIApiKey = _configuration["OpenAI_Key"] ?? throw new InvalidOperationException("OpenAI API key not configured");
        _mockDataPath = Path.Combine(_environment.ContentRootPath, "Data", "data.json.txt");
        
        // Configure HTTP client
        _httpClient.DefaultRequestHeaders.Add("Authorization", $"Bearer {_openAIApiKey}");
        //_httpClient.DefaultRequestHeaders.Add("User-Agent", "SilvrBear-Amazon-Automation/1.0");
        _httpClient.Timeout = TimeSpan.FromMinutes(5); // OpenAI can be slow for vision
    }

    /// <summary>
    /// Processes an image to extract shipment box data using OpenAI Vision API
    /// </summary>
    public async Task<ImageProcessingResult> ProcessImageAsync(IFormFile imageFile, List<string> availableProductNames)
    {
        if (_useMockData)
        {
            var mockResult = new ImageProcessingResult();
            try
            {
                _logger.LogInformation("Using mock data for image processing.");
                var mockDataContent = await File.ReadAllTextAsync(_mockDataPath);
                mockDataContent = mockDataContent.Replace("```json", "").Replace("```", "").Trim();
                mockDataContent = FixJsonFormat(mockDataContent);
                var openAIResponse = new OpenAIResponse
                {
                    Choices = new List<OpenAIChoice>
                    {
                        new OpenAIChoice
                        {
                            Message = new OpenAIResponseMessage
                            {
                                Content = mockDataContent
                            }
                        }
                    }
                };
                var extractedData = ParseOpenAIResponse(openAIResponse);
                mockResult.ProcessedBoxes = ProcessExtractedData(extractedData);
                mockResult.IsSuccess = mockResult.ProcessedBoxes.Any();
                if (!mockResult.IsSuccess)
                {
                    mockResult.Errors.Add(ShipmentConstants.ErrorMessages.NoBoxesFound);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing mock data: {Error}", ex.Message);
                mockResult.Errors.Add($"Failed to process mock data: {ex.Message}");
            }
            return mockResult;
        }

        var stopwatch = Stopwatch.StartNew();
        var result = new ImageProcessingResult();

        try
        {
            _logger.LogInformation("Starting image processing for file: {FileName}", imageFile.FileName);

            // Validate image
            var validation = await ValidateImageAsync(imageFile);
            if (!validation.IsValid)
            {
                result.Errors.AddRange(validation.Errors);
                return result;
            }

            // Save image temporarily
            var imagePath = await SaveImageTemporarilyAsync(imageFile);
            result.ProcessedImagePath = imagePath;

            try
            {
                // Convert image to base64
                var base64Image = await ConvertImageToBase64Async(imageFile);

                // Create OpenAI request
                var openAIRequest = CreateOpenAIRequest(base64Image, availableProductNames);

                // Call OpenAI API
                var openAIResponse = await CallOpenAIAPIAsync(openAIRequest);

                // Parse response
                var extractedData = ParseOpenAIResponse(openAIResponse);

                // Process extracted data
                result.ProcessedBoxes = ProcessExtractedData(extractedData);
                result.IsSuccess = result.ProcessedBoxes.Any();

                if (!result.IsSuccess)
                {
                    result.Errors.Add(ShipmentConstants.ErrorMessages.NoBoxesFound);
                }

                _logger.LogInformation("Image processing completed. Found {BoxCount} boxes", result.ProcessedBoxes.Count);
            }
            finally
            {
                // Cleanup temporary file
                await CleanupTemporaryImageAsync(imagePath);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing image: {Error}", ex.Message);
            result.Errors.Add(string.Format(ShipmentConstants.ErrorMessages.OpenAIProcessingFailed, ex.Message));
        }
        finally
        {
            stopwatch.Stop();
            result.ProcessingTime = stopwatch.Elapsed;
        }

        return result;
    }

    /// <summary>
    /// Processes an image from file path
    /// </summary>
    public Task<ImageProcessingResult> ProcessImageFromPathAsync(string imagePath, List<string> availableProductNames)
    {
        throw new NotImplementedException("ProcessImageFromPathAsync is not used in this build.");
    }

    /// <summary>
    /// Validates image file before processing
    /// </summary>
    public async Task<ImageValidationResult> ValidateImageAsync(IFormFile imageFile)
    {
        var result = new ImageValidationResult
        {
            FileSizeBytes = imageFile.Length,
            FileExtension = Path.GetExtension(imageFile.FileName).ToLower(),
            MimeType = imageFile.ContentType
        };

        // Check file size
        if (imageFile.Length > ShipmentConstants.FileProcessing.MaxImageSizeBytes)
        {
            result.Errors.Add(string.Format(ShipmentConstants.ErrorMessages.ImageTooLarge, ShipmentConstants.FileProcessing.MaxImageSizeMB));
        }

        // Check file extension
        if (!ShipmentConstants.FileProcessing.SupportedImageFormats.Contains(result.FileExtension))
        {
            result.Errors.Add(ShipmentConstants.ErrorMessages.InvalidImageFormat);
        }

        // Check if file is empty
        if (imageFile.Length == 0)
        {
            result.Errors.Add("Image file is empty");
        }

        result.IsValid = !result.Errors.Any();
        return await Task.FromResult(result);
    }

    /// <summary>
    /// Converts image to base64 for OpenAI API
    /// </summary>
    public async Task<string> ConvertImageToBase64Async(IFormFile imageFile)
    {
        using var memoryStream = new MemoryStream();
        await imageFile.CopyToAsync(memoryStream);
        var imageBytes = memoryStream.ToArray();
        var base64String = Convert.ToBase64String(imageBytes);
        var mimeType = imageFile.ContentType;
        
        return $"data:{mimeType};base64,{base64String}";
    }

    /// <summary>
    /// Saves uploaded image to temporary location
    /// </summary>
    public async Task<string> SaveImageTemporarilyAsync(IFormFile imageFile)
    {
        var tempDir = Path.Combine(_environment.ContentRootPath, "temp", "images");
        Directory.CreateDirectory(tempDir);

        var fileName = $"{Guid.NewGuid()}{Path.GetExtension(imageFile.FileName)}";
        var filePath = Path.Combine(tempDir, fileName);

        using var fileStream = new FileStream(filePath, FileMode.Create);
        await imageFile.CopyToAsync(fileStream);

        return filePath;
    }

    /// <summary>
    /// Cleans up temporary image files
    /// </summary>
    public async Task<bool> CleanupTemporaryImageAsync(string imagePath)
    {
        try
        {
            if (File.Exists(imagePath))
            {
                File.Delete(imagePath);
                return true;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to cleanup temporary image: {ImagePath}", imagePath);
        }

        return await Task.FromResult(false);
    }

    /// <summary>
    /// Tests OpenAI API connectivity
    /// </summary>


    /// <summary>
    /// Gets OpenAI API usage statistics
    /// </summary>


    #region Private Helper Methods

    private OpenAIRequest CreateOpenAIRequest(string base64Image, List<string> availableProductNames)
    {
        var productNamesText = string.Join(", ", availableProductNames);
        var systemPrompt = ShipmentConstants.OpenAI.ImageProcessingPrompt + 
                          $"\n\nAvailable product names to match against:\n{productNamesText}";

        _logger.LogInformation("Creating OpenAI request using model: {Model}", ShipmentConstants.OpenAI.VisionModel);

        return new OpenAIRequest
        {
            Model = ShipmentConstants.OpenAI.VisionModel,
            MaxTokens = ShipmentConstants.OpenAI.MaxTokens,
            Temperature = ShipmentConstants.OpenAI.Temperature,
            Messages = new List<OpenAIMessage>
            {
                new()
                {
                    Role = "user",
                    Content = new List<OpenAIContent>
                    {
                        new()
                        {
                            Type = "text",
                            Text = systemPrompt
                        },
                        new()
                        {
                            Type = "image_url",
                            ImageUrl = new OpenAIImageUrl
                            {
                                Url = base64Image
                            }
                        }
                    }
                }
            }
        };
    }

    // Helper to fix the mock JSON format (quick and dirty for dev use)
    private string FixJsonFormat(string input)
    {
        // Replace unquoted keys and values with quoted ones
        var fixedJson = input
            .Replace("boxes [", "\"boxes\": [")
            .Replace("boxNumber ", "\"boxNumber\": ")
            .Replace("productName ", "\"productName\": ")
            .Replace("barcodeType ", "\"barcodeType\": ")
            .Replace("quantity ", "\"quantity\": ")
            .Replace("dimensions ", "\"dimensions\": ")
            .Replace("remarks ", "\"remarks\": ")
            .Replace(",\n", ",\n") // clean up trailing commas
            .Replace("\n}", "\n},"); // ensure commas between objects
        // Remove trailing comma before closing array
        fixedJson = System.Text.RegularExpressions.Regex.Replace(fixedJson, ",\\s*]", "]");
        return fixedJson;
    }

    private async Task<OpenAIResponse> CallOpenAIAPIAsync(OpenAIRequest request)
    {
        if (_useMockData)
        {
            _logger.LogInformation("Using mock data from file: {MockDataPath}", _mockDataPath);
            if (!File.Exists(_mockDataPath))
                throw new FileNotFoundException($"Mock data file not found: {_mockDataPath}");
            var mockDataContent = await File.ReadAllTextAsync(_mockDataPath);
            mockDataContent = mockDataContent.Replace("```json", "").Replace("```", "").Trim();
            mockDataContent = FixJsonFormat(mockDataContent);
            _logger.LogInformation("Using mock data: {MockData}", mockDataContent);
            return new OpenAIResponse
            {
                Choices = new List<OpenAIChoice>
                {
                    new OpenAIChoice
                    {
                        Message = new OpenAIResponseMessage
                        {
                            Content = mockDataContent
                        }
                    }
                }
            };
        }

        _usageStats.TotalRequests++;
        var stopwatch = Stopwatch.StartNew();

        try
        {
            // Get the base64 image and prompt from the request
            var userMessage = request.Messages.FirstOrDefault(m => m.Role == "user");
            var textContent = userMessage?.Content?.FirstOrDefault(c => c.Type == "text");
            var imageContent = userMessage?.Content?.FirstOrDefault(c => c.Type == "image_url");
            
            var systemPrompt = textContent?.Text ?? "";
            var base64Image = imageContent?.ImageUrl?.Url ?? "";

            // Create the payload in the exact same format as the working implementation
            var payload = new
            {
                model = request.Model,
                messages = new object[]
                {
                    new
                    {
                        role = "system",
                        content = systemPrompt
                    },
                    new
                    {
                        role = "user",
                        content = new object[]
                        {
                            new
                            {
                                type = "text",
                                text = "Extract all text from this image and organize it as a structured JSON array. Pay special attention to product names and try to match them with the provided catalog when possible."
                            },
                            new
                            {
                                type = "image_url",
                                image_url = new
                                {
                                    url = base64Image
                                }
                            }
                        }
                    }
                },
                max_tokens = request.MaxTokens,
                temperature = request.Temperature
            };

            // Log the request for debugging
            var json = JsonSerializer.Serialize(payload, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = true
            });
            _logger.LogInformation("OpenAI Request JSON: {RequestJson}", json);

            // Use PostAsJsonAsync like the working implementation
            var response = await _httpClient.PostAsJsonAsync("https://api.openai.com/v1/chat/completions", payload);

            stopwatch.Stop();
            _usageStats.LastRequestTime = DateTime.UtcNow;

            var responseContent = await response.Content.ReadAsStringAsync();
            _logger.LogInformation("OpenAI Response Status: {StatusCode}, Content: {ResponseContent}", 
                response.StatusCode, responseContent);

            if (!response.IsSuccessStatusCode)
            {
                _usageStats.FailedRequests++;
                throw new HttpRequestException($"OpenAI API call failed: {response.StatusCode} - {responseContent}");
            }

            // Parse the response like the working implementation
            var responseJson = await response.Content.ReadFromJsonAsync<JsonElement>();
            var resultText = responseJson.GetProperty("choices")[0].GetProperty("message").GetProperty("content").GetString() ?? "No response from API";

            // Create a simplified response object
            var openAIResponse = new OpenAIResponse
            {
                Choices = new List<OpenAIChoice>
                {
                    new OpenAIChoice
                    {
                        Message = new OpenAIResponseMessage
                        {
                            Content = resultText
                        }
                    }
                }
            };

            // Update usage stats if available
            if (responseJson.TryGetProperty("usage", out var usageElement))
            {
                if (usageElement.TryGetProperty("total_tokens", out var totalTokens))
                    _usageStats.TotalTokensUsed += totalTokens.GetInt32();
                if (usageElement.TryGetProperty("prompt_tokens", out var promptTokens))
                    _usageStats.TotalPromptTokens += promptTokens.GetInt32();
                if (usageElement.TryGetProperty("completion_tokens", out var completionTokens))
                    _usageStats.TotalCompletionTokens += completionTokens.GetInt32();
            }

            _usageStats.SuccessfulRequests++;
            return openAIResponse;
        }
        catch
        {
            _usageStats.FailedRequests++;
            throw;
        }
    }

    private OpenAIImageResponse ParseOpenAIResponse(OpenAIResponse response)
    {
        if (response.Choices?.Any() != true)
        {
            throw new InvalidOperationException("No choices in OpenAI response");
        }

        var content = response.Choices.First().Message.Content;
        if (string.IsNullOrWhiteSpace(content))
        {
            throw new InvalidOperationException("Empty content in OpenAI response");
        }

        try
        {
            // Clean up the response (remove any markdown formatting)
            content = content.Trim();
            if (content.StartsWith("```json"))
            {
                content = content.Substring(7);
            }
            if (content.EndsWith("```"))
            {
                content = content.Substring(0, content.Length - 3);
            }

            var options = new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                PropertyNameCaseInsensitive = true
            };

            return JsonSerializer.Deserialize<OpenAIImageResponse>(content, options) 
                   ?? new OpenAIImageResponse();
        }
        catch (JsonException ex)
        {
            _logger.LogError(ex, "Failed to parse OpenAI response as JSON: {Content}", content);
            throw new InvalidOperationException($"Invalid JSON response from OpenAI: {ex.Message}");
        }
    }

    private List<ProcessedBoxData> ProcessExtractedData(OpenAIImageResponse extractedData)
    {
        var processedBoxes = new List<ProcessedBoxData>();

        foreach (var box in extractedData.Boxes)
        {
            var processedBox = new ProcessedBoxData
            {
                BoxNumber = box.BoxNumber,
                OriginalProductName = box.ProductName,
                MappedProductName = box.ProductName, // Will be updated by mapping service
                BarcodeType = box.BarcodeType,
                Quantity = box.Quantity,
                DimensionsString = box.Dimensions,
                Remarks = box.Remarks
            };

            // Basic validation
            ValidateProcessedBox(processedBox);
            processedBoxes.Add(processedBox);
        }

        return processedBoxes;
    }

    private void ValidateProcessedBox(ProcessedBoxData box)
    {
        box.IsValid = true;

        if (box.BoxNumber < ShipmentConstants.Validation.MinBoxNumber || 
            box.BoxNumber > ShipmentConstants.Validation.MaxBoxNumber)
        {
            box.ValidationErrors.Add($"Invalid box number: {box.BoxNumber}");
            box.IsValid = false;
        }

        if (string.IsNullOrWhiteSpace(box.OriginalProductName))
        {
            box.ValidationErrors.Add("Product name is required");
            box.IsValid = false;
        }

        if (box.Quantity < ShipmentConstants.Validation.MinQuantity || 
            box.Quantity > ShipmentConstants.Validation.MaxQuantity)
        {
            box.ValidationErrors.Add($"Invalid quantity: {box.Quantity}");
            box.IsValid = false;
        }

        if (string.IsNullOrWhiteSpace(box.DimensionsString))
        {
            box.ValidationWarnings.Add("Dimensions not provided");
        }
    }

    private static string GetContentType(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLower();
        return extension switch
        {
            ".jpg" or ".jpeg" => "image/jpeg",
            ".png" => "image/png",
            ".bmp" => "image/bmp",
            ".gif" => "image/gif",
            _ => "application/octet-stream"
        };
    }

    #endregion
}
