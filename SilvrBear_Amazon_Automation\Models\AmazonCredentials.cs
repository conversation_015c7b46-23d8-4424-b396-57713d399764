namespace SilvrBear_Amazon_Automation.Models;

/// <summary>
/// Configuration class for Amazon Seller API sandbox credentials
/// </summary>
public class AmazonCredentials
{
    /// <summary>
    /// Amazon Seller API Client ID (Application ID)
    /// </summary>
    public string ClientId { get; set; } = "YOUR_SANDBOX_CLIENT_ID";

    /// <summary>
    /// Amazon Seller API Client Secret
    /// </summary>
    public string ClientSecret { get; set; } = "YOUR_SANDBOX_CLIENT_SECRET";

    /// <summary>
    /// Amazon Seller API Refresh Token
    /// </summary>
    public string RefreshToken { get; set; } = "YOUR_SANDBOX_REFRESH_TOKEN";

    /// <summary>
    /// Amazon Marketplace ID (US Sandbox: ATVPDKIKX0DER)
    /// </summary>
    public string MarketplaceId { get; set; } = "ATVPDKIKX0DER";

    /// <summary>
    /// Amazon Seller ID (Merchant ID)
    /// </summary>
    public string SellerId { get; set; } = "YOUR_SANDBOX_SELLER_ID";

    /// <summary>
    /// Amazon Seller API Base URL for Sandbox
    /// </summary>
    public string BaseUrl { get; set; } = "https://sellingpartnerapi-na.amazon.com";

    /// <summary>
    /// Login with Amazon (LWA) Token URL
    /// </summary>
    public string LwaTokenUrl { get; set; } = "https://api.amazon.com/auth/o2/token";

    /// <summary>
    /// AWS Region for API calls
    /// </summary>
    public string AwsRegion { get; set; } = "us-east-1";

    /// <summary>
    /// Role ARN for API access (if using IAM role)
    /// </summary>
    public string? RoleArn { get; set; }

    /// <summary>
    /// Indicates if this is a sandbox environment
    /// </summary>
    public bool IsSandbox { get; set; } = true;
}
