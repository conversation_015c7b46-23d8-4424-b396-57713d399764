# GitHub Copilot Instructions for SilvrBear Amazon Automation

## Project Overview
This is an ASP.NET Core Blazor Server application that automates Amazon inbound shipment creation by processing handwritten table images using OpenAI Vision API and mapping products from Excel files.

## Key Architecture Components

### Services Layer
- **ProductMappingService**: Loads and manages product data from Excel files (`Data/Mapping.xlsx`)
- **OpenAIImageService**: Processes images using OpenAI Vision API to extract shipment data
- **ImageToShipmentService**: Orchestrates the complete workflow from image to Amazon shipment
- **AmazonSellerApiService**: Handles Amazon SP-API integration for shipment creation
- **InboundShipmentService**: Manages inbound shipment operations

### Models
- **ProcessedBoxData**: Represents extracted box data with weight calculations
- **ProductMapping**: Product information from Excel mapping file
- **ConsolidatedItemDetails**: SKU-based consolidation with total quantities
- **InboundShipmentModels**: Amazon API request/response models

### Key Constants
- **ShipmentConstants**: Contains all application constants including weights, conversions, and validation rules
- Default item weight: 250g, Default box weight: 100g

## Coding Guidelines

### Weight Calculations
- **Item Weight**: `ItemWeightKg` from Excel mapping
- **Total Item Weight**: `TotalItemWeightKg = ItemWeightKg × Quantity` (simple multiplication)
- **Box Weight**: `BoxWeightKg = 100g` (empty packaging)
- **Total Box Weight**: `TotalBoxWeightKg = TotalItemWeightKg + BoxWeightKg` (for Amazon API)

**Important**: UI should display simple multiplication (quantity × unit weight) without additional packaging weight unless specifically for Amazon API submission.

### Excel Integration
- Uses EPPlus library for Excel file processing
- Product mapping file: `Data/Mapping.xlsx`
- Key columns: "Product Name", "SKU", "Item Weight in Kgs", "Barcode Type"
- Weight column detection includes multiple variations: "item weight in kgs", "weight", "unit weight", etc.

### Image Processing
- OpenAI Vision API extracts table data from handwritten images
- Expected table columns: Box #, Product Name, Barcode Type, Quantity, Dimensions, Remarks
- Product name fuzzy matching using Levenshtein distance (threshold: 0.7)

### Error Handling
- Comprehensive validation at each processing step
- Detailed logging using ILogger for debugging
- Graceful fallbacks (default weights, generated SKUs)

## File Structure Patterns

### Services
- Interface definitions in `I{ServiceName}.cs`
- Implementations in `{ServiceName}.cs`
- Dependency injection registration in `Program.cs`

### Components
- Razor components in `Components/Pages/`
- Shared layout in `Components/Layout/`
- CSS modules for component-specific styling

### Models
- Domain models grouped by functionality
- Request/Response models for API integration
- Validation attributes for data integrity

## Best Practices

### When Adding New Features
1. Follow existing service pattern with interface + implementation
2. Add comprehensive logging for debugging
3. Include validation and error handling
4. Update constants in `ShipmentConstants` if needed
5. Add unit tests following existing patterns

### When Modifying Weight Calculations
1. Maintain distinction between item weight and total box weight
2. Update UI to show appropriate weight type for context
3. Ensure Amazon API gets packaging-inclusive weights
4. Update logging messages for clarity

### When Working with Excel
1. Use case-insensitive column matching
2. Support multiple column name variations
3. Provide meaningful error messages for missing columns
4. Log column mapping results for debugging

### When Processing Images
1. Validate image format and size before processing
2. Handle OpenAI API rate limits and errors
3. Provide clear feedback on extraction results
4. Log processing statistics for monitoring

## Common Patterns

### Service Registration
```csharp
builder.Services.AddScoped<IServiceName, ServiceImplementation>();
builder.Services.AddHttpClient<IServiceName, ServiceImplementation>();
```

### Logging Pattern
```csharp
_logger.LogInformation("Operation completed: {Detail}", detail);
_logger.LogWarning("Potential issue: {Issue}", issue);
_logger.LogError(ex, "Error in {Method}: {Message}", nameof(Method), ex.Message);
```

### Weight Calculation Pattern
```csharp
// Simple multiplication for display
var totalItemWeight = quantity * unitWeight;

// Include packaging for Amazon API
var totalBoxWeight = totalItemWeight + packageWeight;
```

## Dependencies
- **EPPlus**: Excel file processing
- **Microsoft.Extensions.Http**: HTTP client services
- **System.Text.Json**: JSON serialization
- **Bootstrap**: UI framework
- **Blazor Server**: Interactive web framework

## Performance Notes
- Product mapping is cached in memory after first load
- Image processing is async to avoid blocking UI
- Large Excel files are processed row by row
- HTTP clients are reused via dependency injection

When suggesting code changes, ensure they align with these patterns and maintain the existing architecture's consistency.
