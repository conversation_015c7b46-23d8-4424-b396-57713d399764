using SilvrBear_Amazon_Automation.Models;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Interface for OpenAI image processing operations
/// </summary>
public interface IOpenAIImageService
{
    /// <summary>
    /// Processes an image to extract shipment box data using OpenAI Vision API
    /// </summary>
    /// <param name="imageFile">Image file to process</param>
    /// <param name="availableProductNames">List of available product names for matching</param>
    /// <returns>Extracted box data from the image</returns>
    Task<ImageProcessingResult> ProcessImageAsync(IFormFile imageFile, List<string> availableProductNames);

    /// <summary>
    /// Validates image file before processing
    /// </summary>
    /// <param name="imageFile">Image file to validate</param>
    /// <returns>Validation result</returns>
    Task<ImageValidationResult> ValidateImageAsync(IFormFile imageFile);

    /// <summary>
    /// Converts image to base64 for OpenAI API
    /// </summary>
    /// <param name="imageFile">Image file to convert</param>
    /// <returns>Base64 encoded image data</returns>
    Task<string> ConvertImageToBase64Async(IFormFile imageFile);

    /// <summary>
    /// Saves uploaded image to temporary location
    /// </summary>
    /// <param name="imageFile">Image file to save</param>
    /// <returns>Path to saved image</returns>
    Task<string> SaveImageTemporarilyAsync(IFormFile imageFile);

    /// <summary>
    /// Cleans up temporary image files
    /// </summary>
    /// <param name="imagePath">Path to image file to delete</param>
    /// <returns>True if successfully deleted</returns>
    Task<bool> CleanupTemporaryImageAsync(string imagePath);
}

/// <summary>
/// OpenAI API usage statistics
/// </summary>
public class OpenAIUsageStatistics
{
    public int TotalRequests { get; set; }
    public int SuccessfulRequests { get; set; }
    public int FailedRequests { get; set; }
    public int TotalTokensUsed { get; set; }
    public int TotalPromptTokens { get; set; }
    public int TotalCompletionTokens { get; set; }
    public DateTime LastRequestTime { get; set; }
    public TimeSpan AverageResponseTime { get; set; }
    public decimal EstimatedCost { get; set; }
}
