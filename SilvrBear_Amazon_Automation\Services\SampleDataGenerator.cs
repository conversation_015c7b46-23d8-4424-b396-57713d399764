using OfficeOpenXml;
using SilvrBear_Amazon_Automation.Constants;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Service to generate sample data files for testing
/// </summary>
public class SampleDataGenerator
{
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<SampleDataGenerator> _logger;

    public SampleDataGenerator(IWebHostEnvironment environment, ILogger<SampleDataGenerator> logger)
    {
        _environment = environment;
        _logger = logger;
        
        // Set EPPlus license context
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    /// <summary>
    /// Creates a sample mapping Excel file
    /// </summary>
    public async Task CreateSampleMappingFileAsync()
    {
        var dataDir = Path.Combine(_environment.ContentRootPath, "Data");
        Directory.CreateDirectory(dataDir);
        
        var filePath = Path.Combine(dataDir, ShipmentConstants.FileProcessing.MappingFileName);

        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Product Mapping");

        // Add headers
        worksheet.Cells[1, 1].Value = "Product Name";
        worksheet.Cells[1, 2].Value = "SKU";
        worksheet.Cells[1, 3].Value = "Barcode Type";
        worksheet.Cells[1, 4].Value = "Weight";
        worksheet.Cells[1, 5].Value = "Category";
        worksheet.Cells[1, 6].Value = "Description";
        worksheet.Cells[1, 7].Value = "Active";

        // Style headers
        using (var range = worksheet.Cells[1, 1, 1, 7])
        {
            range.Style.Font.Bold = true;
            range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
            range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
        }

        // Add sample data
        var sampleData = new[]
        {
            new { ProductName = "Skinny Black", SKU = "SKU-SB-001", BarcodeType = "old", Weight = 0.25m, Category = "Clothing", Description = "Black skinny jeans", Active = true },
            new { ProductName = "Skinny Blue", SKU = "SKU-SB-002", BarcodeType = "old", Weight = 0.25m, Category = "Clothing", Description = "Blue skinny jeans", Active = true },
            new { ProductName = "Regular Fit Black", SKU = "SKU-RF-001", BarcodeType = "new", Weight = 0.30m, Category = "Clothing", Description = "Regular fit black jeans", Active = true },
            new { ProductName = "Regular Fit Blue", SKU = "SKU-RF-002", BarcodeType = "new", Weight = 0.30m, Category = "Clothing", Description = "Regular fit blue jeans", Active = true },
            new { ProductName = "Slim Fit Black", SKU = "SKU-SF-001", BarcodeType = "old", Weight = 0.28m, Category = "Clothing", Description = "Slim fit black jeans", Active = true },
            new { ProductName = "Slim Fit Blue", SKU = "SKU-SF-002", BarcodeType = "old", Weight = 0.28m, Category = "Clothing", Description = "Slim fit blue jeans", Active = true },
            new { ProductName = "Straight Cut Black", SKU = "SKU-SC-001", BarcodeType = "new", Weight = 0.32m, Category = "Clothing", Description = "Straight cut black jeans", Active = true },
            new { ProductName = "Straight Cut Blue", SKU = "SKU-SC-002", BarcodeType = "new", Weight = 0.32m, Category = "Clothing", Description = "Straight cut blue jeans", Active = true },
            new { ProductName = "Bootcut Black", SKU = "SKU-BC-001", BarcodeType = "old", Weight = 0.35m, Category = "Clothing", Description = "Bootcut black jeans", Active = true },
            new { ProductName = "Bootcut Blue", SKU = "SKU-BC-002", BarcodeType = "old", Weight = 0.35m, Category = "Clothing", Description = "Bootcut blue jeans", Active = true },
            new { ProductName = "Wide Leg Black", SKU = "SKU-WL-001", BarcodeType = "new", Weight = 0.40m, Category = "Clothing", Description = "Wide leg black jeans", Active = true },
            new { ProductName = "Wide Leg Blue", SKU = "SKU-WL-002", BarcodeType = "new", Weight = 0.40m, Category = "Clothing", Description = "Wide leg blue jeans", Active = true },
            new { ProductName = "High Waist Black", SKU = "SKU-HW-001", BarcodeType = "old", Weight = 0.27m, Category = "Clothing", Description = "High waist black jeans", Active = true },
            new { ProductName = "High Waist Blue", SKU = "SKU-HW-002", BarcodeType = "old", Weight = 0.27m, Category = "Clothing", Description = "High waist blue jeans", Active = true },
            new { ProductName = "Low Rise Black", SKU = "SKU-LR-001", BarcodeType = "new", Weight = 0.24m, Category = "Clothing", Description = "Low rise black jeans", Active = true },
            new { ProductName = "Low Rise Blue", SKU = "SKU-LR-002", BarcodeType = "new", Weight = 0.24m, Category = "Clothing", Description = "Low rise blue jeans", Active = true },
            new { ProductName = "Distressed Black", SKU = "SKU-DB-001", BarcodeType = "old", Weight = 0.26m, Category = "Clothing", Description = "Distressed black jeans", Active = true },
            new { ProductName = "Distressed Blue", SKU = "SKU-DB-002", BarcodeType = "old", Weight = 0.26m, Category = "Clothing", Description = "Distressed blue jeans", Active = true },
            new { ProductName = "Classic Black", SKU = "SKU-CB-001", BarcodeType = "new", Weight = 0.29m, Category = "Clothing", Description = "Classic black jeans", Active = true },
            new { ProductName = "Classic Blue", SKU = "SKU-CB-002", BarcodeType = "new", Weight = 0.29m, Category = "Clothing", Description = "Classic blue jeans", Active = true }
        };

        for (int i = 0; i < sampleData.Length; i++)
        {
            var row = i + 2; // Start from row 2 (after header)
            var data = sampleData[i];
            
            worksheet.Cells[row, 1].Value = data.ProductName;
            worksheet.Cells[row, 2].Value = data.SKU;
            worksheet.Cells[row, 3].Value = data.BarcodeType;
            worksheet.Cells[row, 4].Value = data.Weight;
            worksheet.Cells[row, 5].Value = data.Category;
            worksheet.Cells[row, 6].Value = data.Description;
            worksheet.Cells[row, 7].Value = data.Active;
        }

        // Auto-fit columns
        worksheet.Cells.AutoFitColumns();

        // Save the file
        await package.SaveAsAsync(new FileInfo(filePath));
        
        _logger.LogInformation("Sample mapping file created at: {FilePath}", filePath);
    }

    /// <summary>
    /// Creates sample data directory structure
    /// </summary>
    public async Task CreateSampleDataStructureAsync()
    {
        var dataDir = Path.Combine(_environment.ContentRootPath, "Data");
        Directory.CreateDirectory(dataDir);

        // Create temp directories
        var tempDir = Path.Combine(_environment.ContentRootPath, "temp");
        Directory.CreateDirectory(tempDir);
        Directory.CreateDirectory(Path.Combine(tempDir, "images"));

        // Create mapping file if it doesn't exist
        var mappingFile = Path.Combine(dataDir, ShipmentConstants.FileProcessing.MappingFileName);
        if (!File.Exists(mappingFile))
        {
            await CreateSampleMappingFileAsync();
        }

        _logger.LogInformation("Sample data structure created successfully");
    }
}
