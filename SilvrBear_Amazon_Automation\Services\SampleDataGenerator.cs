using OfficeOpenXml;
using SilvrBear_Amazon_Automation.Constants;

namespace SilvrBear_Amazon_Automation.Services;

/// <summary>
/// Service to generate sample data files for testing
/// </summary>
public class SampleDataGenerator
{
    private readonly IWebHostEnvironment _environment;
    private readonly ILogger<SampleDataGenerator> _logger;

    public SampleDataGenerator(IWebHostEnvironment environment, ILogger<SampleDataGenerator> logger)
    {
        _environment = environment;
        _logger = logger;
        
        // Set EPPlus license context
        ExcelPackage.LicenseContext = LicenseContext.NonCommercial;
    }

    /// <summary>
    /// Creates a sample mapping Excel file
    /// </summary>
    public async Task CreateSampleMappingFileAsync()
    {
        var dataDir = Path.Combine(_environment.ContentRootPath, "Data");
        Directory.CreateDirectory(dataDir);
        
        var filePath = Path.Combine(dataDir, ShipmentConstants.FileProcessing.MappingFileName);

        using var package = new ExcelPackage();
        var worksheet = package.Workbook.Worksheets.Add("Product Mapping");

        // Add headers
        worksheet.Cells[1, 1].Value = "Product Name";
        worksheet.Cells[1, 2].Value = "SKU";
        worksheet.Cells[1, 3].Value = "Barcode Type";
        worksheet.Cells[1, 4].Value = "Weight";
        worksheet.Cells[1, 5].Value = "Category";
        worksheet.Cells[1, 6].Value = "Description";
        worksheet.Cells[1, 7].Value = "Active";

        // Style headers
        using (var range = worksheet.Cells[1, 1, 1, 7])
        {
            range.Style.Font.Bold = true;
            range.Style.Fill.PatternType = OfficeOpenXml.Style.ExcelFillStyle.Solid;
            range.Style.Fill.BackgroundColor.SetColor(System.Drawing.Color.LightGray);
        }

        // Add sample data with realistic weights (in kg) - includes products from mock data
        var sampleData = new[]
        {
            // Core products that match mock data exactly
            new { ProductName = "Skinny Black", SKU = "SB-SKINNY-6-BLACK", BarcodeType = "", Weight = 0.04m, Category = "Clothing", Description = "Black skinny jeans", Active = true },
            new { ProductName = "Skinny Classic", SKU = "SB-SKINNY-CLASSIC", BarcodeType = "", Weight = 0.042m, Category = "Clothing", Description = "Classic skinny jeans", Active = true },
            new { ProductName = "Earthy 5", SKU = "SB-5-EARTHY-SCRUNCHIES", BarcodeType = "", Weight = 0.025m, Category = "Accessories", Description = "Earthy scrunchies set", Active = true },
            new { ProductName = "Pastel 5", SKU = "SB-5-PASTEL-SCRUNCHIES", BarcodeType = "", Weight = 0.026m, Category = "Accessories", Description = "Pastel scrunchies set", Active = true },
            new { ProductName = "Black Velvet", SKU = "SB-VLVT-BLACK-8", BarcodeType = "", Weight = 0.026m, Category = "Accessories", Description = "Black velvet accessory", Active = true },
            new { ProductName = "Floral Ribbon", SKU = "SB-FLORAL-RIBBON", BarcodeType = "", Weight = 0.025m, Category = "Accessories", Description = "Floral ribbon accessory", Active = true },
            new { ProductName = "HB Girls Floral Roses", SKU = "SB-GIRLS-HB-FLORAL-ROSES", BarcodeType = "", Weight = 0.025m, Category = "Hair Accessories", Description = "Girls floral roses headband", Active = true },
            new { ProductName = "HB Girls Mauve", SKU = "SB-GIRLS-HB-MAUVE-", BarcodeType = "", Weight = 0.026m, Category = "Hair Accessories", Description = "Girls mauve headband", Active = true },
            new { ProductName = "HB Women Floral Roses", SKU = "SB-WOMEN-HB-FLORAL-ROSES", BarcodeType = "", Weight = 0.028m, Category = "Hair Accessories", Description = "Women floral roses headband", Active = true },
            new { ProductName = "HB Women Polka Vibrant", SKU = "SB-WOMEN-HB-POLKA-VIBRANT", BarcodeType = "", Weight = 0.027m, Category = "Hair Accessories", Description = "Women polka vibrant headband", Active = true },
            new { ProductName = "Purple 3", SKU = "SB-PURPLE-3", BarcodeType = "NEW", Weight = 0.03m, Category = "Accessories", Description = "Purple accessory set", Active = true },
            new { ProductName = "Set 1", SKU = "SB-SET-1", BarcodeType = "", Weight = 0.035m, Category = "Sets", Description = "Accessory set 1", Active = true },
            new { ProductName = "Triple Tone", SKU = "SB-TRIPLE-TONE", BarcodeType = "", Weight = 0.032m, Category = "Accessories", Description = "Triple tone accessory", Active = true },
            new { ProductName = "HB Women Gray Pink", SKU = "SB-WOMEN-HB-GRAY-PINK", BarcodeType = "", Weight = 0.027m, Category = "Hair Accessories", Description = "Women gray pink headband", Active = true },
            new { ProductName = "HB Girls Polka Vibrant", SKU = "SB-GIRLS-HB-POLKA-VIBRANT", BarcodeType = "", Weight = 0.025m, Category = "Hair Accessories", Description = "Girls polka vibrant headband", Active = true },
            new { ProductName = "HB Women Floral Orchids", SKU = "SB-WOMEN-HB-FLORAL-ORCHIDS", BarcodeType = "", Weight = 0.028m, Category = "Hair Accessories", Description = "Women floral orchids headband", Active = true },

            // Additional standard products
            new { ProductName = "Skinny Blue", SKU = "SKU-SB-002", BarcodeType = "old", Weight = 0.04m, Category = "Clothing", Description = "Blue skinny jeans", Active = true },
            new { ProductName = "Regular Fit Black", SKU = "SKU-RF-001", BarcodeType = "new", Weight = 0.045m, Category = "Clothing", Description = "Regular fit black jeans", Active = true },
            new { ProductName = "Regular Fit Blue", SKU = "SKU-RF-002", BarcodeType = "new", Weight = 0.045m, Category = "Clothing", Description = "Regular fit blue jeans", Active = true },
            new { ProductName = "Slim Fit Black", SKU = "SKU-SF-001", BarcodeType = "old", Weight = 0.042m, Category = "Clothing", Description = "Slim fit black jeans", Active = true }
        };

        for (int i = 0; i < sampleData.Length; i++)
        {
            var row = i + 2; // Start from row 2 (after header)
            var data = sampleData[i];
            
            worksheet.Cells[row, 1].Value = data.ProductName;
            worksheet.Cells[row, 2].Value = data.SKU;
            worksheet.Cells[row, 3].Value = data.BarcodeType;
            worksheet.Cells[row, 4].Value = data.Weight;
            worksheet.Cells[row, 5].Value = data.Category;
            worksheet.Cells[row, 6].Value = data.Description;
            worksheet.Cells[row, 7].Value = data.Active;
        }

        // Auto-fit columns
        worksheet.Cells.AutoFitColumns();

        // Save the file
        await package.SaveAsAsync(new FileInfo(filePath));
        
        _logger.LogInformation("Sample mapping file created at: {FilePath}", filePath);
    }

    /// <summary>
    /// Creates sample data directory structure
    /// </summary>
    public async Task CreateSampleDataStructureAsync()
    {
        var dataDir = Path.Combine(_environment.ContentRootPath, "Data");
        Directory.CreateDirectory(dataDir);

        // Create temp directories
        var tempDir = Path.Combine(_environment.ContentRootPath, "temp");
        Directory.CreateDirectory(tempDir);
        Directory.CreateDirectory(Path.Combine(tempDir, "images"));

        // Create mapping file if it doesn't exist
        var mappingFile = Path.Combine(dataDir, ShipmentConstants.FileProcessing.MappingFileName);
        if (!File.Exists(mappingFile))
        {
            await CreateSampleMappingFileAsync();
        }

        _logger.LogInformation("Sample data structure created successfully");
    }
}
